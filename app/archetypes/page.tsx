'use client';

import React, { useRef, useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  Crown,
  Swords,
  Anchor,
  Building2,
  Music,
  Sword,
  Dog,
  Megaphone,
  Handshake,
  Wallet,
  Ghost,
  CircleDot,
  Wind,
  Shield,
  Square,
  Cpu,
  Dice1,
  Wand2,
  Compass,
  User,
  ArrowUp,
  Scale,
  Eye,
  Flame,
  Hammer,
  Heart,
  Crown as ChessKing,
  Link2,
  ChevronLeft,
  ChevronRight,
  Users,
  MessageSquare,
  TrendingUp,
  Star,
  CheckCircle2,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface ArchetypeCharacteristics {
  strengths: string[];
  challenges: string[];
  bestAt: string[];
  environment: string;
}

interface Archetype {
  id: string;
  name: string;
  icon: React.ElementType;
  description: string;
  category: string;
  characteristics: ArchetypeCharacteristics;
  query: {
    competencies: {
      [key: string]: number[]; // Range of scores for each competency
    };
    traits?: string[];
    keywords?: string[];
  };
}

interface CategoryConfig {
  id: string;
  label: string;
  icon: React.ElementType;
  title: string;
  description: string;
}

const defaultCharacteristics: ArchetypeCharacteristics = {
  strengths: [
    "Placeholder strength 1",
    "Placeholder strength 2",
    "Placeholder strength 3"
  ],
  challenges: [
    "Placeholder challenge 1",
    "Placeholder challenge 2",
    "Placeholder challenge 3"
  ],
  bestAt: [
    "Placeholder skill 1",
    "Placeholder skill 2",
    "Placeholder skill 3"
  ],
  environment: "Placeholder environment description"
};

const archetypesList: Archetype[] = [
  // High Leadership + High Decision Making
  {
    id: "iron-throne",
    name: "The Iron Throne",
    icon: Crown,
    description: "Commanding presence with decisive authority",
    category: "High Leadership + High Decision Making",
    characteristics: {
      strengths: [
        "Decisive decision-making",
        "Clear direction setting",
        "Strong authority"
      ],
      challenges: [
        "May appear intimidating",
        "Can be overly directive",
        "Might struggle with delegation"
      ],
      bestAt: [
        "Crisis management",
        "Strategic planning",
        "Driving results"
      ],
      environment: "Thrives in high-stakes environments where quick, authoritative decisions are needed"
    },
    query: { competencies: {} }
  },
  {
    id: "jedi-master",
    name: "The Jedi Master",
    icon: Sword,
    description: "Wise and strategic leadership",
    category: "High Leadership + High Decision Making",
    characteristics: defaultCharacteristics,
    query: { competencies: {} }
  },
  {
    id: "captain",
    name: "The Captain",
    icon: Anchor,
    description: "Steady guidance through challenges",
    category: "High Leadership + High Decision Making",
    query: { competencies: {} }
  },
  {
    id: "architect",
    name: "The Architect",
    icon: Building2,
    description: "Building and scaling with vision",
    category: "High Leadership + High Decision Making",
    query: { competencies: {} }
  },
  {
    id: "conductor",
    name: "The Conductor",
    icon: Music,
    description: "Orchestrating team harmony",
    category: "High Leadership + High Decision Making",
    query: { competencies: {} }
  },

  // High Leadership + Low Communication
  {
    id: "silent-strategist",
    name: "The Silent Strategist",
    icon: ChessKing,
    description: "Strategic moves without much talk",
    category: "High Leadership + Low Communication",
    query: { competencies: {} }
  },
  {
    id: "lone-wolf",
    name: "The Lone Wolf",
    icon: Dog,
    description: "Independent and self-reliant",
    category: "High Leadership + Low Communication",
    query: { competencies: {} }
  },
  {
    id: "hype-machine",
    name: "The Hype Machine",
    icon: Megaphone,
    description: "All action, minimal dialogue",
    category: "High Leadership + Low Communication",
    query: { competencies: {} }
  },
  {
    id: "diplomat",
    name: "The Diplomat",
    icon: Handshake,
    description: "Building bridges silently",
    category: "High Leadership + Low Communication",
    query: { competencies: {} }
  },
  {
    id: "silent-investor",
    name: "The Silent Investor",
    icon: Wallet,
    description: "Quiet resource allocation",
    category: "High Leadership + Low Communication",
    query: { competencies: {} }
  },

  // Low Leadership + Low Communication
  {
    id: "invisible-boss",
    name: "The Invisible Boss",
    icon: Ghost,
    description: "Minimal presence and interaction",
    category: "Low Leadership + Low Communication",
    query: { competencies: {} }
  },
  {
    id: "black-hole",
    name: "The Black Hole",
    icon: CircleDot,
    description: "Information disappears here",
    category: "Low Leadership + Low Communication",
    query: { competencies: {} }
  },
  {
    id: "chaos-coordinator",
    name: "The Chaos Coordinator",
    icon: Wind,
    description: "Thrives in disorder",
    category: "Low Leadership + Low Communication",
    query: { competencies: {} }
  },
  {
    id: "stone-wall",
    name: "The Stone Wall",
    icon: Shield,
    description: "Impenetrable and unresponsive",
    category: "Low Leadership + Low Communication",
    query: { competencies: {} }
  },
  {
    id: "placeholder",
    name: "The Placeholder",
    icon: Square,
    description: "Temporary or ineffective presence",
    category: "Low Leadership + Low Communication",
    query: { competencies: {} }
  },

  // Pop Culture Archetypes
  {
    id: "iron-man",
    name: "The Iron Man",
    icon: Cpu,
    description: "Tech-savvy and innovative",
    category: "Pop Culture Archetypes",
    query: { competencies: {} }
  },
  {
    id: "dungeon-master",
    name: "The Dungeon Master",
    icon: Dice1,
    description: "Creates and controls scenarios",
    category: "Pop Culture Archetypes",
    query: { competencies: {} }
  },
  {
    id: "gandalf",
    name: "The Gandalf",
    icon: Wand2,
    description: "Wise and mysterious guidance",
    category: "Pop Culture Archetypes",
    query: { competencies: {} }
  },

  // High Leadership + Strategic Focus
  {
    id: "lone-captain",
    name: "The Lone Captain",
    icon: Compass,
    description: "Solo navigation expert",
    category: "High Leadership + Strategic Focus",
    query: { competencies: {} }
  },
  {
    id: "solo-operator",
    name: "The Solo Operator",
    icon: User,
    description: "Independent achiever",
    category: "High Leadership + Strategic Focus",
    query: { competencies: {} }
  },
  {
    id: "cheerleader",
    name: "The Cheerleader",
    icon: ArrowUp,
    description: "Motivational force",
    category: "High Leadership + Strategic Focus",
    query: { competencies: {} }
  },
  {
    id: "peacemaker",
    name: "The Peacemaker",
    icon: Scale,
    description: "Balanced decision maker",
    category: "High Leadership + Strategic Focus",
    query: { competencies: {} }
  },
  {
    id: "invisible-investor",
    name: "The Invisible Investor",
    icon: Eye,
    description: "Watchful resource manager",
    category: "High Leadership + Strategic Focus",
    query: { competencies: {} }
  },

  // High Leadership + Team Building
  {
    id: "trailblazer",
    name: "The Trailblazer",
    icon: Flame,
    description: "Innovation and forward movement",
    category: "High Leadership + Team Building",
    query: { competencies: {} }
  },
  {
    id: "builder",
    name: "The Builder",
    icon: Hammer,
    description: "Team and culture builder",
    category: "High Leadership + Team Building",
    query: { competencies: {} }
  },
  {
    id: "glue",
    name: "The Glue",
    icon: Heart,
    description: "Team cohesion specialist",
    category: "High Leadership + Team Building",
    query: { competencies: {} }
  },
  {
    id: "strategist",
    name: "The Strategist",
    icon: Crown,
    description: "Long-term planner",
    category: "High Leadership + Team Building",
    query: { competencies: {} }
  },
  {
    id: "connector",
    name: "The Connector",
    icon: Link2,
    description: "Relationship builder",
    category: "High Leadership + Team Building",
    query: { competencies: {} }
  }
];

const categories: CategoryConfig[] = [
  {
    id: "High Leadership + High Decision Making",
    label: "Elite Leaders",
    icon: Crown,
    title: "Elite Leadership Styles",
    description: "Discover the traits of highly effective leaders who excel in both leadership and decision-making. These leaders demonstrate exceptional ability to guide teams and make impactful choices."
  },
  {
    id: "High Leadership + Low Communication",
    label: "Silent Leaders",
    icon: Sword,
    title: "Action-Oriented Leaders",
    description: "Explore leadership styles that prioritize action over words. These leaders may be quiet, but their impact speaks volumes through their decisions and results."
  },
  {
    id: "Low Leadership + Low Communication",
    label: "Passive Leaders",
    icon: Ghost,
    title: "Hands-Off Leadership",
    description: "Understand the characteristics of leaders who take a more passive approach. While less directive, these styles can sometimes foster independence and self-management in teams."
  },
  {
    id: "Pop Culture Archetypes",
    label: "Pop Culture",
    icon: Cpu,
    title: "Pop Culture Leadership Icons",
    description: "Examine leadership styles inspired by popular culture, offering unique perspectives on modern management approaches through familiar references."
  },
  {
    id: "High Leadership + Strategic Focus",
    label: "Strategists",
    icon: Compass,
    title: "Strategic Leadership Styles",
    description: "Learn about leaders who excel in strategic thinking and planning. These styles emphasize long-term vision and calculated decision-making."
  },
  {
    id: "High Leadership + Team Building",
    label: "Team Builders",
    icon: Users,
    title: "Team-Focused Leadership",
    description: "Explore leadership styles that prioritize team development and collaboration. These leaders excel at building strong, cohesive teams and fostering positive work environments."
  }
];

export default function ArchetypesPage() {
  const [mounted, setMounted] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [selectedArchetype, setSelectedArchetype] = useState(archetypesList[0].id);
  const [matchingBosses, setMatchingBosses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const supabase = createClient();

  useEffect(() => {
    setMounted(true);
  }, []);

  const updateScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const containerWidth = container.clientWidth;
      const scrollAmount = containerWidth - 68;

      container.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      updateScrollButtons();
      container.addEventListener('scroll', updateScrollButtons);
      window.addEventListener('resize', updateScrollButtons);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', updateScrollButtons);
        window.removeEventListener('resize', updateScrollButtons);
      }
    };
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (headerRef.current) {
        const scrollPosition = window.scrollY + 0;
        setIsSticky(scrollPosition > headerRef.current.offsetTop);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Simplified fetchMatchingBosses function
  const fetchMatchingBosses = async (archetype: Archetype) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('bosses')
        .select(`
          *,
          reviews (
            review_id,
            review_content,
            status
          ),
          companies (
            name
          )
        `)
        .is('deleted_at', null)
        .limit(10); // Limit to 10 results initially

      if (error) {
        throw error;
      }

      // Filter for bosses with approved/published reviews
      const bossesWithApprovedReviews = data?.filter(boss =>
        boss.reviews?.some(review =>
          review.status === 'approved' || review.status === 'published'
        )
      ) || [];

      setMatchingBosses(bossesWithApprovedReviews);

    } catch (error) {
      console.error('Error fetching matching bosses:', error);
      setMatchingBosses([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to fetch bosses when archetype changes
  useEffect(() => {
    const archetype = archetypesList.find(a => a.id === selectedArchetype);
    if (archetype) {
      fetchMatchingBosses(archetype);
    }
  }, [selectedArchetype]);

  const selectedArchetypeData = archetypesList.find(a => a.id === selectedArchetype);

  if (!mounted) return null;

  return (
    <div className="min-h-screen bg-background">
      <div className="container max-w-7xl mx-auto py-4 sm:py-6 lg:py-8 px-4 sm:px-6">
        {/* Hero Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Leadership Archetypes</h1>
          <p className="text-base sm:text-xl text-muted-foreground mb-8">
            Discover different leadership styles and what they mean for your career.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-3 sm:grid-cols-5 gap-4 mb-12 text-center">
          <div className="flex flex-col items-center">
            <Users className="h-5 w-5 text-primary mb-1" />
            <p className="text-base font-semibold">30+</p>
            <p className="text-xs text-muted-foreground">Archetypes</p>
          </div>
          <div className="hidden sm:flex flex-col items-center">
            <MessageSquare className="h-5 w-5 text-primary mb-1" />
            <p className="text-base font-semibold">6</p>
            <p className="text-xs text-muted-foreground">Categories</p>
          </div>
          <div className="flex flex-col items-center">
            <Building2 className="h-5 w-5 text-primary mb-1" />
            <p className="text-base font-semibold">Unique</p>
            <p className="text-xs text-muted-foreground">Traits</p>
          </div>
          <div className="flex flex-col items-center">
            <TrendingUp className="h-5 w-5 text-primary mb-1" />
            <p className="text-base font-semibold">Clear</p>
            <p className="text-xs text-muted-foreground">Insights</p>
          </div>
          <div className="hidden sm:flex flex-col items-center">
            <Star className="h-5 w-5 text-primary mb-1" />
            <p className="text-base font-semibold">Expert</p>
            <p className="text-xs text-muted-foreground">Analysis</p>
          </div>
        </div>

        {/* Updated Horizontal Scrolling Section */}
        <div
          ref={headerRef}
          className={cn(
            "relative z-40 transition-shadow",
            isSticky && "bg-background shadow-md"
          )}
        >
          <div className="container max-w-7xl mx-auto px-4 sm:px-6">
            <div className="relative flex items-center justify-between">
              <div
                ref={scrollContainerRef}
                className="flex-1 flex items-center gap-6 overflow-x-auto no-scrollbar py-4"
              >
                {archetypesList.map((archetype) => {
                  const Icon = archetype.icon;
                  const isSelected = selectedArchetype === archetype.id;
                  return (
                    <button
                      key={archetype.id}
                      onClick={() => setSelectedArchetype(archetype.id)}
                      className={cn(
                        "flex flex-col items-center gap-2 min-w-fit transition-colors",
                        "hover:text-primary",
                        isSelected ? "text-primary" : "text-muted-foreground"
                      )}
                    >
                      <div className={cn(
                        "p-2 rounded-md transition-colors",
                        isSelected ? "bg-primary/10" : "bg-transparent",
                        "hover:bg-primary/10"
                      )}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <span className="text-xs font-medium">{archetype.name}</span>
                    </button>
                  );
                })}
              </div>
              <div className="flex gap-2 ml-4">
                <Button
                  variant="outline"
                  size="icon"
                  disabled={!canScrollLeft}
                  onClick={() => scroll('left')}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  disabled={!canScrollRight}
                  onClick={() => scroll('right')}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <Separator />
        </div>

        {/* Updated Characteristics Section */}
        {selectedArchetypeData && (
          <div className="container max-w-7xl mx-auto px-4 sm:px-6">
            <div className="py-8">
              {/* Title and Description */}
              <div className="flex items-start gap-4 mb-8">
                <div className="p-3 rounded-lg bg-primary/10">
                  <selectedArchetypeData.icon className="h-6 w-6 text-primary" />
                </div>
                <div className="space-y-1">
                  <h2 className="text-2xl font-bold tracking-tight">
                    {selectedArchetypeData.name}
                  </h2>
                  <p className="text-muted-foreground">
                    {selectedArchetypeData.description}
                  </p>
                </div>
              </div>

              {/* Characteristics Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {/* Key Strengths */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                    <h3 className="font-semibold text-lg">Key Strengths</h3>
                  </div>
                  <ul className="space-y-3">
                    {selectedArchetypeData.characteristics?.strengths?.map((strength, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-green-500 mt-2" />
                        <span className="text-sm leading-relaxed text-muted-foreground">
                          {strength}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Challenges */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-red-500" />
                    <h3 className="font-semibold text-lg">Challenges</h3>
                  </div>
                  <ul className="space-y-3">
                    {selectedArchetypeData.characteristics?.challenges?.map((challenge, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-red-500 mt-2" />
                        <span className="text-sm leading-relaxed text-muted-foreground">
                          {challenge}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Best At */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Star className="h-5 w-5 text-amber-500" />
                    <h3 className="font-semibold text-lg">Best At</h3>
                  </div>
                  <ul className="space-y-3">
                    {selectedArchetypeData.characteristics?.bestAt?.map((skill, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-amber-500 mt-2" />
                        <span className="text-sm leading-relaxed text-muted-foreground">
                          {skill}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Ideal Environment */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Compass className="h-5 w-5 text-blue-500" />
                    <h3 className="font-semibold text-lg">Ideal Environment</h3>
                  </div>
                  <div className="pl-3.5">
                    <p className="text-sm leading-relaxed text-muted-foreground">
                      {selectedArchetypeData.characteristics?.environment}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Matching Bosses Grid */}
        <div className="container max-w-7xl mx-auto px-4 sm:px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {isLoading ? (
              <div className="col-span-full flex justify-center py-8">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading bosses...</span>
                </div>
              </div>
            ) : matchingBosses.length > 0 ? (
              matchingBosses.map((boss) => (
                <Card key={boss.boss_id} className="hover:bg-accent/5 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4">
                      <div className="p-2 rounded-full bg-primary/10">
                        <Users className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{boss.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {boss.companies?.name || 'Company not specified'}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center py-8 text-muted-foreground">
                No matching bosses found for this archetype.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}