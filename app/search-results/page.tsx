'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { createClient } from '@/utils/supabase/client';
import {routes} from "@/utils/routes";

interface Boss {
  boss_id: number;
  name: string;
}

interface Company {
  company_id: number;
  name: string;
}

const SearchResults: React.FC = () => {
  const searchParams = useSearchParams();
  const query = searchParams?.get('q') || '';
  const [bosses, setBosses] = useState<Boss[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchResults = async () => {
      if (!query) {
        setBosses([]);
        setCompanies([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      const supabase = createClient();

      try {
        const [{ data: bossesData }, { data: companiesData }] = await Promise.all([
          supabase
            .from('bosses')
            .select('boss_id, name')
            .ilike('name', `%${query}%`)
            .is('deleted_at', null)
            .limit(10),
          supabase
            .from('companies')
            .select('company_id, name')
            .ilike('name', `%${query}%`)
            .limit(10)
        ]);

        setBosses(bossesData || []);
        setCompanies(companiesData || []);
      } catch (error) {
        console.error('Error fetching search results:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [query]);

  if (!query) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">Search Results</h1>
        <p>Please enter a search query to find bosses and companies.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">Search Results for "{query}"</h1>

      {isLoading ? (
        <div className="text-center py-8">Loading...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold mb-2">Bosses</h2>
            {bosses.length > 0 ? (
              <ul className="space-y-2">
                {bosses.map((boss) => (
                  <li key={boss.boss_id} className="card p-2 hover-highlight">
                    <Link href={`/boss/${boss.boss_id}`}>{boss.name}</Link>
                  </li>
                ))}
              </ul>
            ) : (
              <p>No bosses found.</p>
            )}
            <div className="mt-4">
              <Link href={routes.addBoss} className="text-primary hover:underline">
                Can't find the boss you're looking for? Add a new boss
              </Link>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-2">Companies</h2>
            {companies.length > 0 ? (
              <ul className="space-y-2">
                {companies.map((company) => (
                  <li
                    key={company.company_id}
                    className="card p-2 hover-highlight"
                  >
                    <Link href={`/company/${company.company_id}`}>{company.name}</Link>
                  </li>
                ))}
              </ul>
            ) : (
              <p>No companies found.</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchResults;