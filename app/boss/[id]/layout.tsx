import type { <PERSON>ada<PERSON> } from "next";
import { ReactNode } from "react";
import { createClient } from "@/utils/supabase/server";

type Props = {
  params: {
    id: string;
  };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const supabase = createClient();

  const { id } = await params;

  const { data: boss } = await supabase
    .from("bosses")
    .select(`name`)
    .eq("boss_id", id)
    .is("deleted_at", null)
    .single();

  const bossName = boss?.name;

  return {
    title: `${bossName ?? "Boss"} | My Amplify`,
    description: `Details about boss ${bossName}.`,
    keywords: [
      "boss",
      "details",
      "info",
      "company",
      "review",
      "amplify",
      bossName,
    ],
    openGraph: {
      title: `${bossName ?? "Boss"} | Amplify`,
      description: `Details about boss ${bossName}.`,
      url: `https://www.boss.reviews/boss/${id}`,
      siteName: "My Website",
      locale: "en_US",
      type: "website",
    },
  };
}

const BossLayout = ({ children }: { children: ReactNode }) => {
  return <section>{children}</section>;
};

export default BossLayout;
