import { Database } from '@/types/database.types';
import { questionnaireToCompetencyMap, competencyCategories } from '../constants/dimensions';

type QuestionnaireResponse = Database['public']['Tables']['questionnaire_responses']['Row'];
type Review = Database['public']['Tables']['reviews']['Row'];

export function calculateCompetencyScores(
  reviews: Review[],
  questionnaireResponses: QuestionnaireResponse[],
) {
  // Debug log initial data
  console.log('Initial Data:', {
    totalReviews: reviews.length,
    reviewRelationships: reviews.map(r => ({
      id: r.review_id,
      relationship: r.relationship
    })),
    totalResponses: questionnaireResponses.length,
    responseReviewIds: questionnaireResponses.map(r => r.review_id)
  });

  // Initialize scores for each competency
  const competencyScores: Record<string, {
    totalScore: number;
    count: number;
    averageScore: number;
    byRelationship: Record<string, {
      totalScore: number;
      count: number;
      averageScore: number;
    }>;
  }> = {};

  // Initialize the structure for each competency
  Object.keys(competencyCategories).forEach(category => {
    competencyScores[category] = {
      totalScore: 0,
      count: 0,
      averageScore: 0,
      byRelationship: {}
    };
  });

  // First, group reviews by relationship to ensure we have all relationships initialized
  const relationshipSet = new Set(reviews.map(review => review.relationship || 'Other'));

  // Initialize all relationships for each competency
  Object.keys(competencyScores).forEach(category => {
    relationshipSet.forEach(relationship => {
      competencyScores[category].byRelationship[relationship] = {
        totalScore: 0,
        count: 0,
        averageScore: 0
      };
    });
  });

  // Process each questionnaire response
  questionnaireResponses.forEach(response => {
    const review = reviews.find(r => r.review_id === response.review_id);
    if (!review) {
      console.warn('No matching review found for response:', response);
      return;
    }

    const relationship = review.relationship || 'Other';
    const competencyCategory = questionnaireToCompetencyMap[response.question_id as keyof typeof questionnaireToCompetencyMap];
    
    if (!competencyCategory) {
      console.warn('No competency mapping found for question:', response.question_id);
      return;
    }

    console.log('Processing response:', {
      reviewId: response.review_id,
      relationship,
      competencyCategory,
      questionId: response.question_id,
      score: response.score
    });

    // Validate and normalize the score from 1-5 scale to -2 to +2 scale
    const rawScore = response.score;
    if (!rawScore || rawScore < 1 || rawScore > 5) {
      console.warn(`Invalid score value: ${rawScore} for question ${response.question_id}`);
      return;
    }

    // 1 -> -2 (Very Negative)
    // 2 -> -1 (Negative)
    // 3 -> 0  (Neutral)
    // 4 -> +1 (Positive)
    // 5 -> +2 (Very Positive)
    const normalizedScore = rawScore

    // Add to total scores
    competencyScores[competencyCategory].totalScore += normalizedScore;
    competencyScores[competencyCategory].count += 1;

    // Add to relationship-specific scores
    competencyScores[competencyCategory].byRelationship[relationship].totalScore += normalizedScore;
    competencyScores[competencyCategory].byRelationship[relationship].count += 1;
  });

  // Calculate averages
  Object.keys(competencyScores).forEach(category => {
    const scores = competencyScores[category];
    
    // Calculate the overall average
    scores.averageScore = scores.count > 0 ? scores.totalScore / scores.count : 0;

    // Calculate relationship-specific averages
    Object.keys(scores.byRelationship).forEach(relationship => {
      const relScores = scores.byRelationship[relationship];
      relScores.averageScore = relScores.count > 0 ? relScores.totalScore / relScores.count : 0;
    });

    // Debug logging
    console.log(`===> ${category} Scores:`, {
      totalScore: scores.totalScore,
      count: scores.count,
      averageScore: scores.averageScore,
      byRelationship: scores.byRelationship,
      relationships: Array.from(relationshipSet)
    });
  });

  return competencyScores;
}

export function getDimensionForScore(category: string, score: number): string {
  const dimensions = competencyCategories[category as keyof typeof competencyCategories]?.dimensions;
  if (!dimensions) return '';

  // Convert score from -2 to +2 scale to dimension index (0-4)
  // -2 -> 0 (Most Negative)
  // -1 -> 1 (Negative)
  //  0 -> 2 (Neutral)
  // +1 -> 3 (Positive)
  // +2 -> 4 (Most Positive)
  const normalizedIndex = Math.round(score + 2);
  const dimensionIndex = Math.max(0, Math.min(4, normalizedIndex));
  
  return dimensions[dimensionIndex];
}

// Helper function to convert between scales
export function convertScoreScale(score: number, fromRange: [number, number], toRange: [number, number]): number {
  const [fromMin, fromMax] = fromRange;
  const [toMin, toMax] = toRange;
  
  // Convert to 0-1 scale
  const normalized = (score - fromMin) / (fromMax - fromMin);
  
  // Convert to target scale
  return normalized * (toMax - toMin) + toMin;
} 