import React from "react";
import { ThumbsUp, ThumbsDown, Star, StarHalf } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Database } from "@/types/database.types";
import { competencyCategories } from "../constants/dimensions";
import { getDimensionForScore } from "../utils/competencyCalculations";
import { Separator } from "@/components/ui/separator";

type BossTrait = Database["public"]["Tables"]["boss_traits"]["Row"] & {
  attribute: Database["public"]["Tables"]["attributes"]["Row"];
};

interface BossTraitsDrawerProps {
  strengthAreas: Array<{
    name: string;
    frequency: number;
    count: number;
  }>;
  improvementAreas: Array<{
    name: string;
    frequency: number;
    count: number;
  }>;
  attributes: Database["public"]["Tables"]["attributes"]["Row"][];
  bossTraits: BossTrait[];
  bossName: string;
  competencyScores: Record<
    string,
    {
      totalScore: number;
      count: number;
      averageScore: number;
      byRelationship: Record<
        string,
        {
          totalScore: number;
          count: number;
          averageScore: number;
        }
      >;
    }
  >;
}

function CompetencyOverview({
  competencyScores,
}: {
  competencyScores: BossTraitsDrawerProps["competencyScores"];
}) {
  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2 md:gap-4">
        {Object.entries(competencyCategories).map(
          ([name, { icon: Icon, dimensions }]) => {
            const score = competencyScores[name]?.averageScore || 0;
            // Convert from -2 to +2 scale to 1-5 scale
            // const normalizedScore = (score + 2) * 1.25 + 1;
            const normalizedScore = score;

            return (
              <div key={name} className="flex items-center gap-3 p-2">
                <div className={cn("p-2 rounded-full bg-primary/10 shrink-0")}>
                  <Icon className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm font-medium whitespace-nowrap">
                    {name}
                  </p>
                  <p className="text-sm font-medium text-muted-foreground">
                    {normalizedScore.toFixed(1)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {getDimensionForScore(name, score)}
                  </p>
                </div>
              </div>
            );
          },
        )}
      </div>
      <div className="flex items-center gap-4 text-xs text-muted-foreground justify-center mt-4">
        <div className="flex items-center gap-1.5">
          <div className="w-2 h-2 rounded-full bg-green-500" />
          <span>4.5+ Strong</span>
        </div>
        <div className="flex items-center gap-1.5">
          <div className="w-2 h-2 rounded-full bg-yellow-500" />
          <span>3.0-4.4 Average</span>
        </div>
        <div className="flex items-center gap-1.5">
          <div className="w-2 h-2 rounded-full bg-red-500" />
          <span>&lt;3.0 Needs Work</span>
        </div>
      </div>
    </div>
  );
}

function StarRating({ score }: { score: number }) {
  // Convert score from -2 to +2 scale to 0-5 scale
  // console.log("===> SCORE BEFOR", score);
  // const normalizedScore = Math.max(1, Math.min(5, (score + 2) * 1.25 + 1));
  const normalizedScore = score
  // console.log("===> SCORE AFTER", normalizedScore);

  const fullStars = Math.floor(normalizedScore);
  const hasHalfStar = normalizedScore % 1 >= 0.5;
  const emptyStars = Math.max(0, 5 - Math.ceil(normalizedScore));

  return (
    <div className="flex items-center gap-0.5">
      {[...Array(fullStars)].map((_, i) => (
        <Star key={`full-${i}`} className="h-3 w-3 fill-primary text-primary" />
      ))}
      {hasHalfStar && <StarHalf className="h-3 w-3 text-primary" />}
      {[...Array(emptyStars)].map((_, i) => (
        <Star key={`empty-${i}`} className="h-3 w-3 text-muted-foreground/20" />
      ))}
      <span className="ml-1.5 text-sm text-muted-foreground">
        {normalizedScore.toFixed(1)}
      </span>
    </div>
  );
}

function CompetencyInsightsSection({
  competencyScores,
  bossName,
}: {
  competencyScores: BossTraitsDrawerProps["competencyScores"];
  bossName: string;
}) {

  console.log("===> COMPETENTT", competencyScores)
  // Sort competencies by score to identify strengths and areas for growth
  const sortedCompetencies = Object.entries(competencyScores)
    .map(([name, data]) => ({
      name,
      score: data.averageScore,
      count: data.count,
      relationships: Object.entries(data.byRelationship).map(
        ([rel, relData]) => ({
          relationship: rel,
          score: relData.averageScore,
          count: relData.count,
        }),
      ),
    }))
    .sort((a, b) => b.score - a.score);

  const strengths = sortedCompetencies.filter((comp) => comp.score > 3);
  const areasForGrowth = sortedCompetencies
    .filter((comp) => comp.score <= 3 )
    .sort((a, b) => a.score - b.score);

  return (
    <section className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Key Strengths */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium flex items-center gap-2">
            <ThumbsUp className="h-4 w-4 text-green-500" />
            Leadership Strengths
          </h3>
          <div className="space-y-6">
            {strengths.map((competency) => {
              const Icon = competencyCategories[competency.name]?.icon;
              return (
                <div key={competency.name} className="space-y-3">
                  {/* Header with Rating */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {Icon && (
                        <div className="p-1 rounded-full bg-primary/10">
                          <Icon className="h-4 w-4 text-primary" />
                        </div>
                      )}
                      <span className="text-sm font-medium">
                        {competency.name}
                      </span>
                    </div>
                    <StarRating score={competency.score} />
                  </div>

                  {/* Relationship Ratings */}
                  <div className="space-y-2 pl-7">
                    {competency.relationships
                      .sort((a, b) => b.score - a.score)
                      .slice(0, 2)
                      .map((rel) => (
                        <div
                          key={rel.relationship}
                          className="flex items-center justify-between text-xs text-muted-foreground"
                        >
                          <span>{rel.relationship}</span>
                          <StarRating score={rel.score} />
                        </div>
                      ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Areas for Growth */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium flex items-center gap-2">
            <ThumbsDown className="h-4 w-4 text-red-500" />
            Growth Opportunities
          </h3>
          <div className="space-y-6">
            {areasForGrowth.map((competency) => {
              const Icon = competencyCategories[competency.name]?.icon;

              return (
                <div key={competency.name} className="space-y-3">
                  {/* Header with Rating */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {Icon && (
                        <div className="p-1 rounded-full bg-primary/10">
                          <Icon className="h-4 w-4 text-primary" />
                        </div>
                      )}
                      <span className="text-sm font-medium">
                        {competency.name}
                      </span>
                    </div>
                    <StarRating score={competency.score} />
                  </div>

                  {/* Relationship Ratings */}
                  <div className="space-y-2 pl-7">
                    {competency.relationships
                      .sort((a, b) => a.score - b.score)
                      .slice(0, 2)
                      .map((rel) => (
                        <div
                          key={rel.relationship}
                          className="flex items-center justify-between text-xs text-muted-foreground"
                        >
                          <span>{rel.relationship}</span>
                          <StarRating score={rel.score} />
                        </div>
                      ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}

export default function BossTraitsDrawer({
  strengthAreas,
  improvementAreas,
  attributes,
  bossTraits,
  bossName,
  competencyScores,
}: BossTraitsDrawerProps) {
  const dimensionScores = calculateDimensionScores();

  return (
    <div className="space-y-12 max-w-full overflow-hidden">
      {/* Core Competencies with detailed scores */}
      <section>
        <div className="overflow-x-auto">
          <div className="pb-4">
            <CompetencyOverview competencyScores={competencyScores} />
          </div>
        </div>
      </section>

      <Separator className={"!mt-0 !md:mt-auto"} />

      {/* Replace the old Key Attributes section with new Competency Insights */}
      <CompetencyInsightsSection
        competencyScores={competencyScores}
        bossName={bossName}
      />
    </div>
  );

  function calculateDimensionScores() {
    const scores: Record<string, number> = {};

    bossTraits.forEach((trait) => {
      const dimension = trait.attribute?.name;
      if (!dimension) return;

      const score =
        trait.attribute.value > 0 ? 1 : trait.attribute.value < 0 ? -1 : 0;
      scores[dimension] = (scores[dimension] || 0) + score;
    });

    return scores;
  }
}
