import React from 'react';
import Image from 'next/image';
import { Separator } from "@/components/ui/separator";
import { Building2, Calendar, MapPin, CheckCircle2, Briefcase, Users, Clock, Award, Linkedin } from 'lucide-react';
import ExperienceTimeline from './ExperienceTimeline';
import type { Boss, WorkExperience } from '@/types/boss';
import { Button } from "@/components/ui/button";

interface AboutBossDrawerProps {
  boss: Boss;
  workExperiences: WorkExperience[];
  totalReports?: number;
  verifiedInfo?: {
    employmentVerified: boolean;
    identityVerified: boolean;
    emailVerified: boolean;
  };
}

export default function AboutBossDrawer({
  boss,
  workExperiences = [],
  totalReports = 0,
  verifiedInfo = {
    employmentVerified: false,
    identityVerified: false,
    emailVerified: false,
  }
}: AboutBossDrawerProps) {
  console.log('Work Experiences:', workExperiences);

  const currentJob = workExperiences
    .sort((a, b) => {
      const dateA = a.start_date ? new Date(a.start_date) : new Date(0);
      const dateB = b.start_date ? new Date(b.start_date) : new Date(0);
      return dateB.getTime() - dateA.getTime();
    })
    .find(exp => exp.is_current);

  const hasWorkExperiences = Array.isArray(workExperiences) && workExperiences.length > 0;

    const bossUniqueCompaniesCount: number = new Set(
        workExperiences.map(exp => exp.company_id)
    ).size;


  return (
    <div className="space-y-8">
      {/* Bio Card */}
      <div className="flex items-start gap-6">
        <Image
          src={'/**********.png'}
          alt={boss.name}
          width={120}
          height={120}
          className="rounded-full border border-border"
          priority
        />
        <div className="flex-1">
          <h3 className="text-2xl font-semibold mb-2">{boss.name}</h3>
          <div className="space-y-2 text-muted-foreground">
            {currentJob && currentJob.company && (
              <div className="flex items-center gap-2 text-sm">
                <Building2 className="h-4 w-4" />
                <span>{currentJob.company.name}</span>
              </div>
            )}
            <div className="flex items-center gap-2 text-sm">
              <Users className="h-4 w-4" />
              <span>Reviewed by {totalReports} team member{totalReports !== 1 ? 's' : ''}</span>
            </div>
            {currentJob?.start_date && (
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4" />
                <span>In current role since {new Date(currentJob.start_date).getFullYear()}</span>
              </div>
            )}
          </div>
          
          {/* LinkedIn Link */}
          {boss.linkedin_url && (
            <Button 
              variant="outline" 
              size="sm"
              className="mt-4"
              asChild
            >
              <a 
                href={boss.linkedin_url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2"
              >
                <Linkedin className="h-4 w-4" />
                View LinkedIn Profile
              </a>
            </Button>
          )}
        </div>
      </div>

      <Separator />

      {/* Current Position */}
      <div className="space-y-4">
        <h4 className="text-base font-medium">Current Position</h4>
        {currentJob && currentJob.company ? (
          <div className="space-y-2 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <div>
                <p className="font-medium text-foreground">{currentJob.job_title}</p>
                <p>{currentJob.company.name}</p>
              </div>
            </div>
            {currentJob.start_date && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>Since {new Date(currentJob.start_date).getFullYear()}</span>
              </div>
            )}
          </div>
        ) : (
          <p className="text-sm text-muted-foreground">No current position information available</p>
        )}
      </div>

      <Separator />

      {/* Verification Status */}
      <div className="space-y-4">
        <h4 className="text-base font-medium">Verification Status</h4>
        <div className="space-y-3">
          {Object.entries(verifiedInfo).map(([key, isVerified]) => (
            isVerified && (
              <div key={key} className="flex items-center gap-3 text-sm">
                <div className="p-1 rounded-full bg-green-500/10">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                </div>
                <span className="text-muted-foreground">
                  {key.replace(/([A-Z])/g, ' $1')
                    .toLowerCase()
                    .replace(/^\w/, c => c.toUpperCase())}
                </span>
              </div>
            )
          ))}
        </div>
      </div>

      <Separator />

      {/* Quick Facts */}
      <div className="space-y-4">
        <h4 className="text-base font-medium">Quick Facts</h4>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-primary/10">
              <Building2 className="h-4 w-4 text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium">
                {bossUniqueCompaniesCount}
              </p>
              <p className="text-xs text-muted-foreground">Companies</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-primary/10">
              <Award className="h-4 w-4 text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium">{totalReports}</p>
              <p className="text-xs text-muted-foreground">Reviews</p>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Work Experience Timeline */}
      <div className="space-y-4">
        <h4 className="text-base font-medium">Work Experience</h4>
        {hasWorkExperiences ? (
          <ExperienceTimeline workExperiences={workExperiences} />
        ) : (
          <p className="text-sm text-muted-foreground">No work experience information available</p>
        )}
      </div>
    </div>
  );
} 