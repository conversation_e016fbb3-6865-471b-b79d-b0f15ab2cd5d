import React, {Di<PERSON>atch, SetStateAction, useEffect, useState} from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Building2, Briefcase, ChevronRight, HelpCircle } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import type { Database } from "@/types/database.types";
import ReputationScoreModal from "./ReputationScoreModal";
import { Drawer, DrawerContent } from "@/components/ui/drawer";
import { useMediaQuery } from "@/hooks/use-media-query";
import { createClient } from "@/utils/supabase/client";
import { useUser } from "@/contexts/UserContext";
import SignInRequiredDialog from "@/components/dialogs/sign-in-required-dialog";
import { usePathname, useRouter } from "next/navigation";

type Boss = Database["public"]["Tables"]["bosses"]["Row"];
type WorkExperience =
  Database["public"]["Tables"]["work_experiences"]["Row"] & {
    company?: { name: string; location?: string };
  };

interface BossProfileCardProps {
  boss: Boss;
  onReviewClick: () => void;
  onViewProfile: () => void;
  onViewReviews: () => void;
  workExperiences?: WorkExperience[];
  averageRating: number;
  stats?: {
    totalReviews: number;
    reputationScore: number | null;
    promoterRate: number | null;
    relationshipScores?: {
      "direct report"?: { count: number; score: number };
      "direct manager/supervisor"?: { count: number; score: number };
      "co-worker"?: { count: number; score: number };
    };
  };
  isNewProfile?: boolean;
  handleOpenAreYouDialog: () => void;
}

export default function BossProfileCard({
  boss,
  onReviewClick,
  onViewProfile,
  onViewReviews,
  workExperiences = [],
  averageRating,
  stats = {
    totalReviews: 0,
    reputationScore: null,
    promoterRate: null,
  },
  isNewProfile = false,
  handleOpenAreYouDialog,
}: BossProfileCardProps) {
  const { user, savedBosses, refreshUser } = useUser();
  const pathname = usePathname();
  const router = useRouter();

  const [showReviewForm, setShowReviewForm] = useState(false);
  const [showReputationModal, setShowReputationModal] = useState(false);
  const [showSignInRequiredDialog, setShowSignInRequiredDialog] =
    useState<boolean>(false);
  const [showBossDialog, setShowBossDialog] = useState(false);
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const [isSaved, setIsSaved] = useState(
    savedBosses.some((save) => save.boss_id === boss.boss_id),
  );

  const handleSaveBoss = async () => {
    try {
      if (!user) {
        setShowSignInRequiredDialog(true);
      } else {
        const supabase = createClient();
        if (isSaved) {
          setIsSaved(false);
          const { error: deleteError } = await supabase
            .from("saved_bosses")
            .delete()
            .eq("user_id", user.id)
            .eq("boss_id", boss.boss_id);
          if (deleteError) throw deleteError;
        } else {
          setIsSaved(true);

          const { error: insertError } = await supabase
            .from("saved_bosses")
            .insert({
              user_id: user.id,
              boss_id: boss.boss_id,
            });
          if (insertError) throw insertError;
        }
      }
    } catch (error) {
      console.error("Error saving boss:", error);
    } finally {
      localStorage.removeItem('follow')
      await refreshUser();
    }
  };

  useEffect(() => {
    const isAutoFollow = localStorage.getItem('follow')

    if (isAutoFollow && !isSaved && user) {
      handleSaveBoss()
    }
  }, []);

  const handleLogin = () => {
    localStorage.setItem('follow', boss.boss_id)
    router.push(`/sign-in?redirect=${encodeURIComponent(pathname)}`);
  };


  const currentJob =
    workExperiences
      .sort((a, b) => {
        const dateA = a.start_date ? new Date(a.start_date) : new Date(0);
        const dateB = b.start_date ? new Date(b.start_date) : new Date(0);
        return dateB.getTime() - dateA.getTime();
      })
      .find((exp) => exp.is_current) || workExperiences[0];

  return (
    <div className="py-6">
      <SignInRequiredDialog
        showDialog={showSignInRequiredDialog}
        setShowDialog={setShowSignInRequiredDialog}
        actionClick={handleLogin}
        description={"Please sign-in to your account to follow bosses."}
      />

      {/* Profile Info - Always visible */}
      <div className="flex flex-col gap-2">
        {/* Desktop Layout */}
        <div className="hidden sm:flex gap-4">
          {/* Avatar column */}
          <div className="shrink-0">
            <Image
              src={"/209560-200.png"}
              alt={boss.name}
              width={56}
              height={56}
              className="rounded-full border border-border"
              priority
            />
          </div>

          {/* Info column */}
          <div className="flex flex-col min-w-0">
            <div className="flex flex-row gap-2 items-center mb-1.5">
              <h1 className="text-2xl font-semibold">{boss.name}</h1>
            </div>
            <div className="flex items-center gap-3 text-sm text-muted-foreground">
              {currentJob?.job_title && (
                <div className="flex items-center gap-1.5">
                  <Briefcase className="h-4 w-4" />
                  <span>{currentJob.job_title}</span>
                </div>
              )}
              {currentJob?.company?.name && (
                <div className="flex items-center gap-1.5">
                  <Building2 className="h-4 w-4" />
                  <span>{currentJob.company.name}</span>
                </div>
              )}
            </div>
          </div>

          {/* Action column */}
          <div className="ml-auto flex flex-row gap-4 ">
            <Button
              variant="outline"
              onClick={handleSaveBoss}
              className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors min-w-24 ${
                isSaved
                  ? "bg-primary text-white hover:bg-primary/90 hover:text-white"
                  : "text-gray-700  hover:bg-primary hover:text-white"
              }`}
            >
              {isSaved ? "Following" : "Follow"}
            </Button>
            <Button
              variant="outline"
              onClick={onViewProfile}
              className="group w-fit"
            >
              View full profile
              <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="sm:hidden flex flex-col gap-2">
          <div className="flex gap-4">
            {/* Avatar */}
            <div className="shrink-0">
              <Image
                src={"/209560-200.png"}
                alt={boss.name}
                width={56}
                height={56}
                className="rounded-full border border-border"
                priority
              />
            </div>

            {/* Info */}
            <div className="flex flex-col min-w-0">
              <h1 className="text-2xl font-semibold mb-1.5">{boss.name}</h1>
              <div className="flex items-center gap-3 text-sm text-muted-foreground">
                {currentJob?.job_title && (
                  <div className="flex items-center gap-1.5">
                    <Briefcase className="h-4 w-4" />
                    <span>{currentJob.job_title}</span>
                  </div>
                )}
                {currentJob?.company?.name && (
                  <div className="flex items-center gap-1.5">
                    <Building2 className="h-4 w-4" />
                    <span>{currentJob.company.name}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* "Are you..." link */}
          <div className={"flex justify-end"}>
            <button
              onClick={handleOpenAreYouDialog}
              className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-primary w-fit"
            >
              <HelpCircle className="h-4 w-4" />
              <span>Are you {boss.name}?</span>
            </button>
          </div>

          <div className={"flex gap-4 justify-center mt-4"}>
            <Button
              variant="outline"
              onClick={handleSaveBoss}
              className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors min-w-24 ${
                isSaved
                  ? "bg-primary text-white hover:bg-primary/90 hover:text-white"
                  : "text-gray-700  hover:bg-primary hover:text-white"
              }`}
            >
              {isSaved ? "Following" : "Follow"}
            </Button>
            {/* View Profile Button */}
            <Button
              variant="outline"
              onClick={onViewProfile}
              className="group w-fit"
            >
              View full profile
              <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </div>

        {/* "Are you..." link - Desktop Only */}
        <div className="hidden sm:flex pl-[28px]  justify-end">
          <button
            onClick={handleOpenAreYouDialog}
            className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-primary w-fit"
          >
            <HelpCircle className="h-4 w-4" />
            <span>Are you {boss.name}?</span>
          </button>
        </div>
      </div>

      {/* Stats & Action - Only show if there are reviews */}
      {!isNewProfile && (
        <>
          <Separator className="my-6" />

          {/* Stats & Action - Responsive layout for all screen sizes */}
          <div className="flex flex-col sm:flex-row w-full gap-6 sm:gap-0 sm:items-center">
            {/* Stats section - Grid layout for better responsiveness */}
            <div className="grid grid-cols-2 sm:flex sm:flex-row sm:items-center">
              {/* Average Rating */}
              <div className="text-center py-2 sm:px-4 md:px-6">
                <div className="text-lg font-semibold truncate">
                  {averageRating ? averageRating.toFixed(2) : "—"}
                </div>
                <div className="text-xs text-muted-foreground">
                  Average Rating
                </div>
              </div>

              {/* Vertical separator - hidden on mobile */}
              <div className="hidden sm:block">
                <Separator orientation="vertical" className="h-8" />
              </div>

              {/* Reputation Score */}
              <button
                onClick={() => setShowReputationModal(true)}
                className="text-center hover:opacity-80 transition-opacity py-2 sm:px-4 md:px-6"
              >
                <div className="text-lg font-semibold truncate">
                  {typeof stats.reputationScore === "number"
                    ? `${Math.round(stats.reputationScore)}%`
                    : "—"}
                </div>
                <div className="text-xs text-muted-foreground">
                  Reputation Score
                </div>
              </button>

              {/* Vertical separator - hidden on mobile */}
              <div className="hidden sm:block">
                <Separator orientation="vertical" className="h-8" />
              </div>

              {/* Reviews */}
              <button
                onClick={onViewReviews}
                className="text-center group py-2 sm:px-4 md:px-6"
              >
                <div className="text-lg font-semibold truncate">
                  {stats.totalReviews}
                </div>
                <div className="text-xs text-muted-foreground group-hover:text-primary group-hover:underline">
                  Reviews
                </div>
              </button>

              {/* Vertical separator - hidden on mobile */}
              <div className="hidden sm:block">
                <Separator orientation="vertical" className="h-8" />
              </div>

              {/* Recommend Rate */}
              <div className="text-center py-2 sm:px-4 md:px-6">
                <div className="text-lg font-semibold truncate">
                  {typeof stats.promoterRate === "number"
                    ? `${stats.promoterRate}%`
                    : "—"}
                </div>
                <div className="text-xs text-muted-foreground">Recommend</div>
              </div>
            </div>

            {/* Action Button - full width on mobile, right-aligned on desktop */}
            <div className="w-full flex justify-center sm:w-auto mt-4 sm:mt-0 sm:ml-auto">
              <Button
                onClick={onReviewClick}
                size="lg"
                className="w-auto sm:w-auto bg-primary hover:bg-primary/90"
              >
                Write a Review
              </Button>
            </div>
          </div>

          {/* Add Reputation Score Modal */}
          {stats.reputationScore !== null && (
            <>
              {isDesktop ? (
                <ReputationScoreModal
                  isOpen={showReputationModal}
                  onClose={() => setShowReputationModal(false)}
                  score={stats.reputationScore}
                  metrics={{
                    directReport: stats.relationshipScores?.["direct report"]
                      ? {
                          count:
                            stats.relationshipScores["direct report"].count,
                          score:
                            stats.relationshipScores["direct report"].score,
                        }
                      : null,
                    directManager: stats.relationshipScores?.[
                      "direct manager/supervisor"
                    ]
                      ? {
                          count:
                            stats.relationshipScores[
                              "direct manager/supervisor"
                            ].count,
                          score:
                            stats.relationshipScores[
                              "direct manager/supervisor"
                            ].score,
                        }
                      : null,
                    coworker: stats.relationshipScores?.["co-worker"]
                      ? {
                          count: stats.relationshipScores["co-worker"].count,
                          score: stats.relationshipScores["co-worker"].score,
                        }
                      : null,
                  }}
                />
              ) : (
                <Drawer
                  open={showReputationModal}
                  onClose={() => setShowReputationModal(false)}
                >
                  <ReputationScoreModal
                    isOpen={showReputationModal}
                    onClose={() => setShowReputationModal(false)}
                    score={stats.reputationScore}
                    metrics={{
                      directReport: stats.relationshipScores?.["direct report"]
                        ? {
                            count:
                              stats.relationshipScores["direct report"].count,
                            score:
                              stats.relationshipScores["direct report"].score,
                          }
                        : null,
                      directManager: stats.relationshipScores?.[
                        "direct manager/supervisor"
                      ]
                        ? {
                            count:
                              stats.relationshipScores[
                                "direct manager/supervisor"
                              ].count,
                            score:
                              stats.relationshipScores[
                                "direct manager/supervisor"
                              ].score,
                          }
                        : null,
                      coworker: stats.relationshipScores?.["co-worker"]
                        ? {
                            count: stats.relationshipScores["co-worker"].count,
                            score: stats.relationshipScores["co-worker"].score,
                          }
                        : null,
                    }}
                  />
                </Drawer>
              )}
            </>
          )}

          <Separator className="mt-6" />
        </>
      )}

      {/* <BossLetterDialog
         isOpen={showBossDialog}
         onOpenChange={setShowBossDialog}
         bossName={boss.name}
      /> */}
    </div>
  );
}
