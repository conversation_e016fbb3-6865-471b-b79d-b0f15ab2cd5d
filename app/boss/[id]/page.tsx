"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import type {
  Boss,
  Review,
  WorkExperience,
  Sentiment,
  QuestionnaireResponses,
} from "@/types/boss";
import {
  Loader2,
  Brain,
  Users,
  Target,
  ChartBar,
  MessageSquare,
  Lightbulb,
  Star,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Drawer } from "@/components/ui/drawer";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ThumbsUp, ThumbsDown, ChevronRight } from "lucide-react";
import { Linkedin } from "lucide-react";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";
import logger from "@/utils/logger";

// Component Imports
import QuickOverview from "./components/QuickOverview";
import ReviewsSection from "./components/ReviewsSection";
import SentimentAnalysis from "./components/SentimentAnalysis";
import NPSBreakdown from "./components/NPSBreakdown";
import BossTraitsSection from "./components/BossTraitsSection";
import InsightsPreview from "./components/InsightsPreview";
import ReviewFormModal from "@/components/ReviewFormModal";
import AboutBossDrawer from "./components/AboutBossDrawer";
import BossTraitsDrawer from "./components/BossTraitsDrawer";
import CompetencyHeatmap from "./components/CompetencyHeatmap";
import { calculateCompetencyScores } from "./utils/competencyCalculations";
import BossProfileCard from "./components/BossProfileCard";
import {
  calculateReputationScore,
  calculatePromoterRate,
  calculateGroupScore,
} from "./utils/reputationScore";
import { useUser } from "@/contexts/UserContext";
import BossLetterDialog from "@/components/dialogs/boss-letter-dialog";

// Define competency categories
const competencyCategories = {
  "Strategic Thinking": { icon: Brain },
  "People Management": { icon: Users },
  "Goal Setting": { icon: Target },
  Performance: { icon: ChartBar },
  Communication: { icon: MessageSquare },
  Innovation: { icon: Lightbulb },
} as const;

type DrawerView =
  | "reviews"
  | "experience"
  | "insights"
  | "traits"
  | "about"
  | "competency"
  | "sentiment"
  | "nps"
  | "areYouBoss"
  | null;

export default function BossPage() {
  const params = useParams();
  const { refreshUser } = useUser();

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDrawer, setOpenDrawer] = useState<DrawerView>(null);
  const [selectedReviewId, setSelectedReviewId] = useState<number | null>(null);

  // Core Data States
  const [boss, setBoss] = useState<Boss | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [workExperiences, setWorkExperiences] = useState<WorkExperience[]>([]);
  const [sentiments, setSentiments] = useState<Sentiment[]>([]);

  // Add state for traits data with proper typing
  const [strengthAreas, setStrengthAreas] = useState<
    Array<{ name: string; frequency: number; count: number }>
  >([]);
  const [improvementAreas, setImprovementAreas] = useState<
    Array<{ name: string; frequency: number; count: number }>
  >([]);
  const [attributes, setAttributes] = useState<any[]>([]);
  const [bossTraits, setBossTraits] = useState<any[]>([]);

  // Add searchTerm to the state
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedBuzzWord, setSelectedBuzzWord] = useState<string>("");
  const [buzzWords, setBuzzWords] = useState<string[]>([]);

  // Add state for word cloud data
  const [wordCloudData, setWordCloudData] = useState<
    Array<{ text: string; value: number }>
  >([]);

  // Add state for review modal
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [isReviewFormOpen, setIsReviewFormOpen] = useState(false);

  // Add media query hook
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Anchor logic for share review logic
  useEffect(() => {
    refreshUser();

    if (typeof window !== "undefined") {
      const hash = window.location.hash;

      if (hash) {
        handleShowAllReviews();
      }
    }
  }, []);

  // Add to existing state
  const [questionnaireResponses, setQuestionnaireResponses] = useState<
    QuestionnaireResponse[]
  >([]);

  // Add to existing state declarations
  const [competencyScores, setCompetencyScores] = useState<
    Record<
      string,
      {
        totalScore: number;
        count: number;
        averageScore: number;
        byRelationship: Record<
          string,
          {
            totalScore: number;
            count: number;
            averageScore: number;
          }
        >;
      }
    >
  >({});

  // Add this to the state declarations at the top
  const [dominantNPSClass, setDominantNPSClass] = useState<
    "Promoter" | "Passive" | "Detractor"
  >("Passive");

  // Add new state for V3 form visibility
  const [isReviewFormV3Open, setIsReviewFormV3Open] = useState(false);

  // Create a reusable title component
  const DrawerTitle = ({ title }: { title: string | React.ReactNode }) => (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setOpenDrawer(null)}
          className="mr-2"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-lg font-semibold">{title}</h2>
      </div>
    </div>
  );

  // Create a reusable content wrapper
  const ContentWrapper = ({ children }: { children: React.ReactNode }) => (
    <div className="p-4 sm:p-6">{children}</div>
  );

  // Render either Dialog or Drawer based on screen size
  const ResponsiveContainer = ({
    isOpen,
    onClose,
    title,
    children,
  }: {
    isOpen: boolean;
    onClose: () => void;
    title: string | React.ReactNode;
    children: React.ReactNode;
  }) => {
    if (isDesktop) {
      return (
        <Dialog open={isOpen} onOpenChange={onClose}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-background border">
            <DialogHeader className="px-6 py-4 border-b">
              <DialogTitle>{title}</DialogTitle>
            </DialogHeader>
            <div className="bg-background">{children}</div>
          </DialogContent>
        </Dialog>
      );
    }

    return (
      <Drawer open={isOpen} onClose={onClose}>
        <DrawerTitle title={title} />
        {children}
      </Drawer>
    );
  };

  // Add handleShowMore function
  const handleShowMore = (reviewId: number) => {
    setSelectedReviewId(reviewId);
    setOpenDrawer("reviews");
  };

  // Separate handlers for different actions
  const handleShowAllReviews = () => {
    setOpenDrawer("reviews");
  };

  const handleWriteReview = () => {
    setIsReviewModalOpen(true);
  };

  const handleReviewStart = () => {
    setIsReviewModalOpen(false);
    setIsReviewFormOpen(true);
  };

  // Add handler for traits drawer
  const handleTraitsClick = () => {
    setOpenDrawer("traits");
  };

  // Fetch Data
  useEffect(() => {
    const fetchBossData = async () => {
      setIsLoading(true);
      try {
        const supabase = createClient();

        logger.info("Fetching boss data:", params.id);

        const { data: bossData, error: bossError } = await supabase
          .from("bosses")
          .select(
            `
            *,
            work_experiences!boss_id (
              *,
              companies!work_experiences_company_id_fkey (
                company_id,
                name,
                industry,
                location
              )
            )
          `,
          )
          .eq("boss_id", params.id)
          .is("deleted_at", null)
          .single();

        if (bossError) {
          if (bossError.code === "PGRST116" || bossError.code === "22P02") {
            return null;
          } else {
            throw bossError;
          }
        }

        // Update reviews query to include company information
        const { data: reviewsData, error: reviewsError } = await supabase
          .from("reviews")
          .select(
            `
            *,
            sentiment_id (*),
            questionnaire_responses (*),
            companies!reviews_company_id_fkey (
              company_id,
              name,
              industry,
              location
            ),
            review_votes:review_votes (
              id,
              is_positive
            )
          `,
          )
          .eq("boss_id", params.id)
          .in("status", ["published", "approved"]);

        if (reviewsError) throw reviewsError;

        // Calculate vote balance for each review
        const enrichedReviews =
          reviewsData?.map((review: any) => {
            const votes = review.review_votes || [];

            const positiveVotes = votes.filter(
              (vote: any) => vote.is_positive,
            ).length;
            const negativeVotes = votes.filter(
              (vote: any) => vote.is_positive === false,
            ).length;

            return {
              ...review,
              vote_balance: positiveVotes - negativeVotes,
              positive_votes: positiveVotes,
              negative_votes: negativeVotes,
            };
          }) || [];

        // Transform work experiences to include company data
        const transformedWorkExperiences = bossData.work_experiences.map(
          (exp) => ({
            ...exp,
            company: exp.companies, // Ensure company data is nested under 'company' property
          }),
        );

        // Calculate NPS classifications first
        const npsClassifications = enrichedReviews.reduce(
          (acc, review) => {
            if (!review.nps_score) return acc;

            if (review.nps_score >= 9) {
              acc.promoters++;
            } else if (review.nps_score >= 7) {
              acc.passives++;
            } else {
              acc.detractors++;
            }
            return acc;
          },
          { promoters: 0, passives: 0, detractors: 0 },
        );

        // Calculate percentages
        const totalResponses = enrichedReviews.length;
        const promoterPercentage =
          (npsClassifications.promoters / totalResponses) * 100;
        const passivePercentage =
          (npsClassifications.passives / totalResponses) * 100;
        const detractorPercentage =
          (npsClassifications.detractors / totalResponses) * 100;

        // Determine dominant NPS classification and set state
        let newDominantNPSClass: "Promoter" | "Passive" | "Detractor";
        if (
          npsClassifications.promoters > npsClassifications.passives &&
          npsClassifications.promoters > npsClassifications.detractors
        ) {
          newDominantNPSClass = "Promoter";
        } else if (
          npsClassifications.passives > npsClassifications.detractors
        ) {
          newDominantNPSClass = "Passive";
        } else {
          newDominantNPSClass = "Detractor";
        }
        setDominantNPSClass(newDominantNPSClass);

        // Calculate sentiment score
        const sentimentScore =
          enrichedReviews.reduce((acc, review) => {
            const sentiment = review.sentiment_id?.sentiment || 0;
            return acc + sentiment;
          }, 0) / (enrichedReviews.length || 1);

        // Calculate competency scores
        const competencyScores = calculateCompetencyScores(
          enrichedReviews,
          enrichedReviews.flatMap((r) => r.questionnaire_responses || []),
        );

        // Replace console.log with logger.info for important state changes
        logger.info("Boss evaluation:", {
          bossName: bossData.name,
          metrics: {
            sentimentScore,
            npsClassification: newDominantNPSClass,
            reviewCount: enrichedReviews.length,
          },
        });

        // Set states
        setBoss(bossData);
        setWorkExperiences(transformedWorkExperiences);
        setReviews(enrichedReviews);
        setCompetencyScores(competencyScores);
        setSentiments(
          enrichedReviews.map((r) => r.sentiment_id).filter(Boolean),
        );

        // Process traits data
        const traitFrequencies: Record<
          string,
          {
            count: number;
            isStrength: boolean;
            frequency: number;
          }
        > = {};

        enrichedReviews.forEach((review) => {
          if (review.sentiment_id?.attribute) {
            const attributeName = review.sentiment_id.attribute.name;
            const isStrength = review.sentiment_id.sentiment > 0;

            if (!traitFrequencies[attributeName]) {
              traitFrequencies[attributeName] = {
                count: 0,
                isStrength,
                frequency: 0,
              };
            }
            traitFrequencies[attributeName].count += 1;
          }
        });

        // Calculate frequencies
        const totalTraits = Object.values(traitFrequencies).reduce(
          (sum, { count }) => sum + count,
          0,
        );

        Object.values(traitFrequencies).forEach((trait) => {
          trait.frequency = trait.count / totalTraits;
        });

        // Split into strength and improvement areas
        const strengths = Object.entries(traitFrequencies)
          .filter(([_, data]) => data.isStrength)
          .map(([name, data]) => ({
            name,
            frequency: data.frequency,
            count: data.count,
          }))
          .sort((a, b) => b.frequency - a.frequency);

        const improvements = Object.entries(traitFrequencies)
          .filter(([_, data]) => !data.isStrength)
          .map(([name, data]) => ({
            name,
            frequency: data.frequency,
            count: data.count,
          }))
          .sort((a, b) => b.frequency - a.frequency);

        // Set all states
        setStrengthAreas(strengths);
        setImprovementAreas(improvements);
        setBossTraits(
          Object.entries(traitFrequencies).map(([name, data]) => ({
            name,
            frequency: data.frequency,
            count: data.count,
            isStrength: data.isStrength,
          })),
        );
        setAttributes(
          Object.entries(traitFrequencies).map(([name, data]) => ({
            name,
            frequency: data.frequency,
            count: data.count,
          })),
        );
      } catch (error) {
        logger.error("Failed to fetch boss data:", error);
        setError("Failed to load boss data");
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchBossData();
    }
  }, [params.id]);

  useEffect(() => {
    // Only open the review form if we have following state AND the user explicitly started a review
    const savedState = localStorage.getItem(`bossReview_${params.id}`);
    if (savedState) {
      const { wasReviewStarted } = JSON.parse(savedState);
      if (wasReviewStarted) {
        setIsReviewFormOpen(true);
      }
    }
  }, [params.id]);

  // Calculate overview stats
  const totalReviews = reviews.length;
  const avgRating =
    reviews.reduce((sum, r) => sum + (r.nps_score || 0), 0) / totalReviews || 0;
  const avgSentiment =
    sentiments.reduce((sum, s) => sum + s.sentiment, 0) / sentiments.length ||
    0;
  const totalCompanies = new Set(workExperiences.map((w) => w.company_id)).size;
  const recentSentiments = sentiments.slice(-5);
  const trendScore = recentSentiments.reduce((sum, s) => sum + s.sentiment, 0);
  const recentTrend =
    trendScore > 0 ? "up" : trendScore < 0 ? "down" : "neutral";

  // Update NPS calculations
  const promoters = reviews.filter((r) => r.nps_score >= 4).length;
  const detractors = reviews.filter((r) => r.nps_score <= 2).length;
  const npsScore =
    (promoters / reviews.length - detractors / reviews.length) * 100;

  // Calculate sentiment score
  const sentimentScore =
    reviews.reduce((acc, review) => {
      const sentiment = review.sentiment_id?.sentiment || 0;
      return acc + sentiment;
    }, 0) / reviews.length || 0;

  // Add detailed console logs for evaluation inputs
  logger.log("Boss Evaluation Inputs:", {
    npsScore: {
      value: npsScore,
      details: {
        promoters,
        detractors,
        totalReviews: reviews.length,
      },
    },
    sentimentScore: {
      value: sentimentScore,
      details: {
        totalSentiment: reviews.reduce(
          (acc, review) => acc + (review.sentiment_id?.sentiment || 0),
          0,
        ),
        reviewCount: reviews.length,
      },
    },
    competencyScores: {
      scores: competencyScores,
      averages: Object.entries(competencyScores).map(([category, data]) => ({
        category,
        averageScore: data.averageScore,
        totalResponses: data.count,
      })),
    },
  });

  // Log the evaluation thresholds being used
  logger.log("Evaluation Thresholds:", {
    great: "sentimentScore >= 1.2 && npsScore >= 50 && avgCompetency <= 2.5",
    toxic: "sentimentScore <= -1.3 || npsScore < 0 || avgCompetency >= 3.5",
    current: {
      sentimentScore,
      npsScore,
      avgCompetency:
        Object.values(competencyScores).reduce(
          (sum, { averageScore }) => sum + averageScore,
          0,
        ) / Object.keys(competencyScores).length,
    },
  });

  // Add handler for review submission
  const handleReviewSubmit = async () => {
    await fetchBossData();
  };

  // Update the review click handler
  const handleReviewClick = () => {
    setIsReviewModalOpen(true);
  };

  // Update handleWordClick
  const handleWordClick = (word: string) => {
    setSelectedBuzzWord(word);
    setOpenDrawer("reviews");
  };

  // Calculate average sentiment
  const calculateAverageSentiment = () => {
    if (!sentiments.length) return 0;
    return (
      sentiments.reduce((sum, s) => sum + s.sentiment, 0) / sentiments.length
    );
  };

  const hasReviews = reviews.length > 0;

  // For the zero state preview, we'll show a subset of categories
  const previewCategories = [
    "Strategic Thinking",
    "People Management",
    "Communication",
    "Performance",
  ];

  // Add this helper function to transform review data into the format needed
  const aggregateReviewMetrics = (reviews: Review[]): ReputationMetrics => {
    logger.info("Aggregating review metrics", {
      totalReviews: reviews.length,
    });

    // Group reviews by relationship type
    const groupedReviews = reviews.reduce((acc, review) => {
      const relationship = review.relationship?.toLowerCase() || "other";
      if (!acc[relationship]) {
        acc[relationship] = [];
      }

      const metrics = {
        sentimentScore: review.sentiment_id?.sentiment || 0,
        npsScore: review.nps_score ? review.nps_score * 20 - 100 : 0,
        competencyScores: {
          leadership: getCompetencyScore(review, "leadership"),
          communication: getCompetencyScore(review, "communication"),
          technical: getCompetencyScore(review, "technical"),
          strategic: getCompetencyScore(review, "strategic"),
          interpersonal: getCompetencyScore(review, "interpersonal"),
          innovation: getCompetencyScore(review, "innovation"),
        },
      };

      acc[relationship].push(metrics);
      return acc;
    }, {} as ReputationMetrics);

    return groupedReviews;
  };

  // Helper function to safely get competency scores
  const getCompetencyScore = (review: Review, competency: string): number => {
    const response = review.questionnaire_responses?.find(
      (r) => r.question_id === competency,
    );
    return response?.score || 0;
  };

  // Stats calculation
  const stats = {
    totalReviews: reviews.length,
    reputationScore:
      reviews.length > 0
        ? (() => {
            const metrics = aggregateReviewMetrics(reviews);

            // Calculate individual relationship scores
            const relationshipScores = Object.entries(metrics).reduce(
              (acc, [relationship, relationshipMetrics]) => {
                const groupScore = calculateGroupScore(relationshipMetrics);
                acc[relationship] = {
                  count: relationshipMetrics.length,
                  score: groupScore,
                };
                return acc;
              },
              {} as Record<string, { count: number; score: number }>,
            );

            return {
              total: calculateReputationScore(metrics),
              relationshipScores,
            };
          })()
        : null,
    promoterRate: calculatePromoterRate(reviews),
  };

  // Add handler for V3 form
  const handleReviewV3Submit = async () => {
    setIsReviewFormV3Open(false);
    await fetchBossData();
  };

  // Loading & Error States
  if (isLoading) return <LoadingState />;
  if (error) return <ErrorState message={error} />;
  if (!boss) return <NotFoundState />;

  // Add these calculations before the return statement
  const topStrengths = strengthAreas
    .sort((a, b) => b.frequency - a.frequency)
    .slice(0, 3);

  const topImprovements = improvementAreas
    .sort((a, b) => b.frequency - a.frequency)
    .slice(0, 3);

  const handleOpenAreYouDialog = () => {
    setOpenDrawer("areYouBoss");
  };

  return (
    <div className="container max-w-5xl mx-auto px-4 md:px-8 py-8">
      <div className="space-y-8">
        {/* Profile Card - Always visible */}
        <BossProfileCard
          boss={boss}
          workExperiences={workExperiences}
          onReviewClick={handleWriteReview}
          onViewProfile={() => setOpenDrawer("about")}
          onViewReviews={() => setOpenDrawer("reviews")}
          averageRating={avgRating}
          stats={{
            totalReviews: stats.totalReviews,
            reputationScore: stats.reputationScore
              ? stats.reputationScore.total
              : null,
            promoterRate: stats.promoterRate,
            relationshipScores: stats.reputationScore?.relationshipScores,
          }}
          isNewProfile={!hasReviews}
          handleOpenAreYouDialog={handleOpenAreYouDialog}
        />

        {hasReviews ? (
          <>
            <section>
              <h2 className="text-xl font-semibold mb-6">
                What you should know about {boss?.name}
              </h2>
              <QuickOverview
                totalReviews={reviews.length}
                averageRating={avgRating}
                averageSentiment={sentimentScore}
                recentTrend={recentTrend}
                npsScore={npsScore}
                sentimentScore={sentimentScore}
                sentiments={sentiments}
                onReviewsClick={() => setOpenDrawer("reviews")}
                strengthAreas={strengthAreas}
                improvementAreas={improvementAreas}
                bossName={boss?.name}
                competencyScores={competencyScores}
                npsClassification={dominantNPSClass}
                reputationScore={
                  stats.reputationScore ? stats.reputationScore.total : null
                }
              />
            </section>

            <Separator />

            {/* Boss Traits Section with View Details button */}
            <section>
              <h2 className="text-xl font-semibold mb-6">The Boss Breakdown</h2>
              <BossTraitsSection
                reviews={reviews}
                strengthAreas={strengthAreas}
                improvementAreas={improvementAreas}
                attributes={attributes}
                bossTraits={bossTraits}
                bossName={boss?.name}
                onShowDetails={() => setOpenDrawer("traits")}
                competencyScores={competencyScores}
              />
              <Button
                variant="outline"
                onClick={() => setOpenDrawer("traits")}
                className="group mt-6"
              >
                View detailed analysis
                <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </section>

            <Separator />

            <section>
              <h2 className="text-xl font-semibold mb-6">
                Your Boss by the Metrics
              </h2>
              <InsightsPreview
                reviews={reviews}
                sentiments={sentiments}
                onOpenDrawer={setOpenDrawer}
                competencyScores={competencyScores}
              />
            </section>

            <Separator />

            <section>
              <h2 id={"412312"} className="text-xl font-semibold mb-6">
                Real Talk: What employees are saying
              </h2>
              <ReviewsSection
                reviews={reviews}
                sentiments={sentiments}
                onShowAll={handleShowAllReviews}
                onShowMore={handleShowMore}
                onReviewClick={handleWriteReview}
                buzzWords={wordCloudData}
                selectedBuzzWord={selectedBuzzWord}
                onBuzzWordSelect={setSelectedBuzzWord}
                isFullView={false}
                bossId={boss.boss_id}
              />
            </section>
          </>
        ) : (
          /* Zero State Content */
          <div className="text-center space-y-4 py-12">
            <h2 className="text-2xl font-semibold">
              Be the first to review {boss?.name}
            </h2>
            <p className="text-muted-foreground max-w-lg mx-auto">
              Your review will help others understand {boss?.name}'s leadership
              style and make informed career decisions.
            </p>
            <div className="flex gap-2 justify-center">
              <Button onClick={handleWriteReview}>Write Review (V2)</Button>
              {/*<Button */}
              {/*  variant="outline"*/}
              {/*  onClick={() => setIsReviewFormV3Open(true)}*/}
              {/*>*/}
              {/*  Try New Review Form (V3)*/}
              {/*</Button>*/}
            </div>
          </div>
        )}
      </div>

      {/* Overall Experience */}
      <ResponsiveContainer
        isOpen={openDrawer === "insights"}
        onClose={() => setOpenDrawer(null)}
        title="Overall Experience"
      >
        <ContentWrapper>
          <SentimentAnalysis
            reviews={reviews}
            sentiments={sentiments}
            averageSentiment={calculateAverageSentiment()}
          />
          <NPSBreakdown reviews={reviews} />
          <CompetencyHeatmap
            reviews={reviews}
            competencyScores={competencyScores}
          />
        </ContentWrapper>
      </ResponsiveContainer>

      {/* NPS Breakdown */}
      <ResponsiveContainer
        isOpen={openDrawer === "nps"}
        onClose={() => setOpenDrawer(null)}
        title="Rating Breakdown"
      >
        <ContentWrapper>
          <NPSBreakdown reviews={reviews} />
        </ContentWrapper>
      </ResponsiveContainer>

      {/* Sentiment Analysis */}
      <ResponsiveContainer
        isOpen={openDrawer === "sentiment"}
        onClose={() => setOpenDrawer(null)}
        title="Sentiment Analysis"
      >
        <ContentWrapper>
          <SentimentAnalysis
            reviews={reviews}
            sentiments={sentiments}
            averageSentiment={calculateAverageSentiment()}
          />
        </ContentWrapper>
      </ResponsiveContainer>

      {/* Competency Matrix */}
      <ResponsiveContainer
        isOpen={openDrawer === "competency"}
        onClose={() => setOpenDrawer(null)}
        title="Competency Matrix"
      >
        <ContentWrapper>
          <CompetencyHeatmap
            reviews={reviews}
            competencyScores={competencyScores}
          />
        </ContentWrapper>
      </ResponsiveContainer>

      {/* Reviews Drawer/Dialog */}
      <ResponsiveContainer
        isOpen={openDrawer === "reviews"}
        onClose={() => setOpenDrawer(null)}
        title={
          <div className="flex items-center">
            <h2 className="text-lg font-semibold">Reviews</h2>
            <Badge variant="outline" className="ml-2">
              {reviews.length}
            </Badge>
          </div>
        }
      >
        <ContentWrapper>
          <ReviewsSection
            reviews={reviews}
            selectedReviewId={selectedReviewId}
            sentiments={sentiments}
            onShowAll={handleShowAllReviews}
            onShowMore={handleShowMore}
            onReviewClick={handleWriteReview}
            buzzWords={wordCloudData}
            selectedBuzzWord={selectedBuzzWord}
            onBuzzWordSelect={setSelectedBuzzWord}
            isFullView={true}
            bossId={boss.boss_id}
          />
        </ContentWrapper>
      </ResponsiveContainer>

      {/* Leadership Analysis */}
      <ResponsiveContainer
        isOpen={openDrawer === "traits"}
        onClose={() => setOpenDrawer(null)}
        title="Leadership Analysis"
      >
        <ContentWrapper>
          <BossTraitsDrawer
            strengthAreas={strengthAreas}
            improvementAreas={improvementAreas}
            attributes={attributes}
            bossTraits={bossTraits}
            bossName={boss?.name || "This leader"}
            competencyScores={competencyScores}
          />
        </ContentWrapper>
      </ResponsiveContainer>

      {/* About Boss */}
      <ResponsiveContainer
        isOpen={openDrawer === "about"}
        onClose={() => setOpenDrawer(null)}
        title={`About ${boss?.name}`}
      >
        <ContentWrapper>
          <AboutBossDrawer
            boss={boss}
            workExperiences={workExperiences}
            totalReports={reviews.length}
            verifiedInfo={{
              employmentVerified: true,
              identityVerified: false,
              emailVerified: true,
            }}
          />
        </ContentWrapper>
      </ResponsiveContainer>

      {/* Introduction Modal */}
      <ReviewFormModal
        open={isReviewModalOpen}
        onClose={() => setIsReviewModalOpen(false)}
        bossId={boss?.boss_id}
        bossName={boss?.name}
        workExperiences={workExperiences}
        onSubmit={handleReviewStart}
      />

      {/* Full Review Form Modal */}
      <ReviewFormModal
        open={isReviewFormOpen}
        onClose={() => setIsReviewFormOpen(false)}
        bossId={boss?.boss_id}
        bossName={boss?.name}
        workExperiences={workExperiences}
        onSubmit={handleReviewSubmit}
      />

      <ResponsiveContainer
        isOpen={openDrawer === "areYouBoss"}
        onClose={() => setOpenDrawer(null)}
        title={`Welcome to Your Page, ${boss.name}. Buckle Up.`}
      >
        <ContentWrapper>
          <BossLetterDialog />
        </ContentWrapper>
      </ResponsiveContainer>
    </div>
  );
}

// State Components
const LoadingState = () => (
  <div className="flex justify-center items-center h-screen">
    <Loader2 className="w-8 h-8 animate-spin text-primary" />
  </div>
);

const ErrorState = ({ message }: { message: string }) => (
  <div className="container mx-auto px-4 py-8 text-center">
    <h1 className="text-2xl font-bold text-red-500 mb-4">Error</h1>
    <p>{message}</p>
  </div>
);

const NotFoundState = () => (
  <div className="container mx-auto px-4 py-8 text-center">
    <h1 className="text-2xl font-bold mb-4">Boss not found</h1>
    <p>The requested boss profile could not be found.</p>
  </div>
);
