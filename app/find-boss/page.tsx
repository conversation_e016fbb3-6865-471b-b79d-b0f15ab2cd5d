'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, User, Search, PlusCircle, ChevronLeft, Coffee } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";
import useDebounce from '@/hooks/useDebounce';
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Steps, Step } from '@/components/ui/steps';
import AddBossOrCompanyForm from '@/components/AddBossOrCompanyForm';
import {routes} from "@/utils/routes";

interface Company {
  company_id: number;
  name: string;
  boss_count: number;
  industry?: string;
}

interface Boss {
  boss_id: number;
  name: string;
  job_titles: string[];
}

interface SearchResult {
  type: 'company' | 'boss';
  id: number;
  name: string;
  subtext: string;
}

interface BossPreview {
  boss_id: number;
  name: string;
  work_experiences: {
    company_name: string;
    job_titles: string[];
  }[];
}

const FindBossPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [bosses, setBosses] = useState<Boss[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showNotListedForm, setShowNotListedForm] = useState(false);
  const [notListedEmail, setNotListedEmail] = useState('');
  const [isLoadingBosses, setIsLoadingBosses] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [selectedBoss, setSelectedBoss] = useState<BossPreview | null>(null);
  const [addFormType, setAddFormType] = useState<'linkedin' | 'manual' | null>(null);
  const [addStep, setAddStep] = useState(0);
  const [showAddForm, setShowAddForm] = useState(false);

  const supabase = createClient();

  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const { data, error } = await supabase
        .from('work_experiences')
        .select('company_id, companies(name), boss_id')
        .order('company_id');

      if (error) throw error;

      if (data) {
        const companyMap = new Map<number, Set<number>>();
        data.forEach(experience => {
          if (!companyMap.has(experience.company_id)) {
            companyMap.set(experience.company_id, new Set([experience.boss_id]));
          } else {
            companyMap.get(experience.company_id)!.add(experience.boss_id);
          }
        });

        const companiesList = Array.from(companyMap.entries()).map(([companyId, bossIds]) => ({
          company_id: companyId,
          name: data.find(exp => exp.company_id === companyId)?.companies?.name || 'Unknown Company',
          boss_count: bossIds.size
        }));

        setCompanies(companiesList);
      } else {
        setCompanies([]);
      }
    } catch (error) {
      console.error('Error in fetchCompanies:', error);
      setError('Failed to fetch companies. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBosses = async (companyId: number) => {
    setIsLoadingBosses(true);
    setError(null);
    try {
      const { data, error } = await supabase
        .from('work_experiences')
        .select(`
          boss_id,
          job_title,
          bosses!inner (
            name,
            deleted_at
          )
        `)
        .eq('company_id', companyId)
        .is('bosses.deleted_at', null);

      if (error) throw error;

      if (data) {
        const bossesMap = new Map<number, Boss>();
        data.forEach(experience => {
          const bossId = experience.boss_id;
          if (bossesMap.has(bossId)) {
            bossesMap.get(bossId)!.job_titles.push(experience.job_title);
          } else {
            bossesMap.set(bossId, {
              boss_id: bossId,
              name: experience.bosses.name,
              job_titles: [experience.job_title]
            });
          }
        });

        setBosses(Array.from(bossesMap.values()));
      } else {
        setBosses([]);
      }
    } catch (error) {
      console.error('Error fetching bosses:', error);
      setError('Failed to fetch bosses. Please try again.');
    } finally {
      setIsLoadingBosses(false);
    }
  };

  const handleCompanySelect = (company: Company) => {
    if (selectedCompany?.company_id === company.company_id) {
      setSelectedCompany(null);
      setBosses([]);
    } else {
      setSelectedCompany(company);
      fetchBosses(company.company_id);
    }
  };

  // Initialize search from URL query parameter
  useEffect(() => {
    const query = searchParams.get('q');
    if (query) {
      setSearchTerm(query);
      handleSearch(query);
    }
  }, [searchParams]);

  const handleSearch = useCallback(async (term: string) => {
    if (term.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    try {
      const [{ data: bosses }, { data: companies }] = await Promise.all([
        supabase
          .from('bosses')
          .select('boss_id, name')
          .ilike('name', `%${term}%`)
          .is('deleted_at', null)
          .limit(5),
        supabase
          .from('companies')
          .select('company_id, name, industry')
          .ilike('name', `%${term}%`)
          .limit(5)
      ]);

      const results: SearchResult[] = [
        ...(bosses?.map(boss => ({
          type: 'boss' as const,
          id: boss.boss_id,
          name: boss.name,
          subtext: 'Boss'
        })) || []),
        ...(companies?.map(company => ({
          type: 'company' as const,
          id: company.company_id,
          name: company.name,
          subtext: company.industry || 'Company'
        })) || [])
      ];

      setSearchResults(results);
    } catch (error) {
      console.error('Error in search:', error);
      setError('Failed to perform search. Please try again.');
    } finally {
      setIsSearching(false);
    }
  }, [supabase]);

  // Update URL when search term changes
  useEffect(() => {
    if (searchTerm) {
      const newUrl = `/find-boss?q=${encodeURIComponent(searchTerm)}`;
      window.history.replaceState({}, '', newUrl);
    }
  }, [searchTerm]);

  const fetchBossPreview = async (bossId: number) => {
    setIsLoadingBosses(true);
    setError(null);
    try {
      const { data, error } = await supabase
        .from('work_experiences')
        .select(`
          boss_id,
          job_title,
          companies (name),
          bosses!inner (name, deleted_at)
        `)
        .eq('boss_id', bossId)
        .is('bosses.deleted_at', null);

      if (error) throw error;

      if (data && data.length > 0) {
        const groupedExperiences = data.reduce((acc, exp) => {
          const companyName = exp.companies?.name || 'Unknown Company';
          if (!acc[companyName]) {
            acc[companyName] = { company_name: companyName, job_titles: [] };
          }
          if (exp.job_title && !acc[companyName].job_titles.includes(exp.job_title)) {
            acc[companyName].job_titles.push(exp.job_title);
          }
          return acc;
        }, {} as Record<string, { company_name: string; job_titles: string[] }>);

        const bossPreview: BossPreview = {
          boss_id: bossId,
          name: data[0].bosses?.name || 'Unknown Boss',
          work_experiences: Object.values(groupedExperiences)
        };
        setSelectedBoss(bossPreview);
      } else {
        setError('No data found for this boss.');
      }
    } catch (error) {
      console.error('Error fetching boss preview:', error);
      setError('Failed to fetch boss preview. Please try again.');
    } finally {
      setIsLoadingBosses(false);
    }
  };

  const handleResultSelect = (result: SearchResult) => {
    if (result.type === 'boss') {
      fetchBossPreview(result.id);
      setSelectedCompany(null);
    } else {
      setSelectedCompany({
        company_id: result.id,
        name: result.name,
        boss_count: 0
      });
      fetchBosses(result.id);
      setSelectedBoss(null);
    }
  };

  const handleNotListedSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    alert('Thank you! We\'ll notify you when we add more companies.');
    setNotListedEmail('');
    setShowNotListedForm(false);
  };

  const handleAddButtonClick = () => {
    setShowNotListedForm(true);
    setAddFormType(null);
    setAddStep(0);
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission based on addFormType
    if (addFormType === 'linkedin') {
      // Handle LinkedIn URL submission
      alert('LinkedIn profile submitted. We\'ll process it and add the information soon!');
    } else {
      // Handle manual submission
      alert('Thank you for adding new information. We\'ll review and add it soon!');
    }
    setNotListedEmail('');
    setShowNotListedForm(false);
    setAddFormType(null);
    setAddStep(0);
  };

  const handleAddFormSubmit = (formData: any) => {
    // Handle the form submission here
    console.log('Form submitted:', formData);
    // You might want to send this data to your backend or perform other actions
    setShowAddForm(false);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Skeleton className="h-10 w-64 mx-auto mb-8" />
        <div className="space-y-8">
          <Skeleton className="h-10 w-full" />
          <div className="space-y-2">
            {[...Array(5)].map((_, index) => (
              <Skeleton key={index} className="h-10 w-full" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8 text-center">
        Find a Boss or Company
      </h1>

      {error && (
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="space-y-8">
        <div className="relative">
          <Input
            type="text"
            placeholder="Search bosses or companies..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 text-base"
            autoFocus={!searchParams.get("q")}
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>

        <AnimatePresence mode="wait">
          {selectedCompany ? (
            <motion.div
              key="selected-company"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>{selectedCompany.name}</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedCompany(null)}
                  >
                    <ChevronLeft className="h-4 w-4 mr-2" /> Back to Companies
                  </Button>
                </CardHeader>
                <CardContent>
                  <h3 className="text-xl font-semibold mb-4">
                    Available Bosses
                  </h3>
                  {isLoadingBosses ? (
                    <div className="space-y-4">
                      {[...Array(3)].map((_, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center">
                            <Skeleton className="h-10 w-10 rounded-full mr-3" />
                            <div>
                              <Skeleton className="h-4 w-32 mb-2" />
                              <Skeleton className="h-3 w-24" />
                            </div>
                          </div>
                          <Skeleton className="h-8 w-16" />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {bosses.map((boss) => (
                        <div
                          key={boss.boss_id}
                          className="flex items-center justify-between rounded-lg"
                        >
                          <div className="flex items-center">
                            <User className="h-10 w-10 text-primary bg-primary/10 p-2 rounded-full mr-3" />
                            <div>
                              <h4 className="font-semibold">{boss.name}</h4>
                              <p className="text-sm text-muted-foreground">
                                {boss.job_titles.join(", ")}
                              </p>
                            </div>
                          </div>
                          <Link href={`/boss/${boss.boss_id}`}>
                            <Button size="sm">View Profile</Button>
                          </Link>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ) : selectedBoss ? (
            <motion.div
              key="selected-boss"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>{selectedBoss.name}</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedBoss(null)}
                  >
                    <ChevronLeft className="h-4 w-4 mr-2" /> Back to Search
                  </Button>
                </CardHeader>
                <CardContent>
                  <h3 className="text-xl font-semibold mb-4">
                    Work Experiences
                  </h3>
                  {isLoadingBosses ? (
                    <div className="space-y-4">
                      {[...Array(3)].map((_, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center">
                            <Skeleton className="h-10 w-10 rounded-full mr-3" />
                            <div>
                              <Skeleton className="h-4 w-32 mb-2" />
                              <Skeleton className="h-3 w-24" />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {selectedBoss.work_experiences.map((exp, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between rounded-lg"
                        >
                          <div className="flex items-center">
                            <Building className="h-10 w-10 text-primary bg-primary/10 p-2 rounded-full mr-3" />
                            <div>
                              <h4 className="font-semibold">
                                {exp.company_name}
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                {exp.job_titles.join(", ")}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  <div className="mt-6">
                    <Link href={`/boss/${selectedBoss.boss_id}`}>
                      <Button size="lg" className="w-full">
                        View Full Profile
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ) : (
            <motion.div
              key="search-results"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3 }}
            >
              {searchTerm ? (
                isSearching ? (
                  <div className="space-y-2">
                    {[...Array(3)].map((_, index) => (
                      <Skeleton key={index} className="h-16 w-full" />
                    ))}
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="space-y-2">
                    {searchResults.map((result) => (
                      <Button
                        key={`${result.type}-${result.id}`}
                        variant="outline"
                        className="w-full py-4 px-6 flex justify-between items-center text-left"
                        onClick={() => handleResultSelect(result)}
                      >
                        <div className="flex items-center flex-grow min-w-0">
                          {result.type === "company" ? (
                            <Building className="mr-2 h-4 w-4 min-w-4" />
                          ) : (
                            <User className="mr-2 h-4 w-4 min-w-4" />
                          )}
                          <span className="font-semibold truncate">
                            {result.name}
                          </span>
                        </div>
                        <span className="text-sm text-muted-foreground whitespace-nowrap pl-4">
                          {result.subtext}
                        </span>
                      </Button>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <PlusCircle className="mx-auto h-12 w-12 text-primary mb-4" />
                      <h3 className="text-xl font-semibold mb-2">
                        No Results Found
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        We couldn't find any bosses or companies matching your
                        search.
                      </p>
                      <Button
                        // onClick={() => setShowAddForm(true)}
                          asChild
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                      >
                        <Link href={routes.addBoss}>
                        Add a Boss or Company - It's Easy!
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                )
              ) : (
                <Card>
                  <CardContent className="p-6 text-center">
                    <motion.div
                      animate={{
                        rotate: [0, -15, 0],
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        repeatDelay: 2,
                        ease: "easeInOut",
                      }}
                    >
                      <Coffee className="mx-auto h-12 w-12 text-primary mb-4" />
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-2">
                      Ready to spill some tea?
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Search for a boss or company to get started.
                    </p>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {showAddForm && (
        <AddBossOrCompanyForm
          onClose={() => setShowAddForm(false)}
          onSubmit={handleAddFormSubmit}
        />
      )}
    </div>
  );
};

export default FindBossPage;
