import Header from "@/components/header";
import { Inter } from "next/font/google";
import type { Metadata } from "next";

import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  icons: {
    icon: "/noun-megaphone-4643277.svg",
  },
};

// Import existing imports
import { UserProvider } from "@/contexts/UserContext";
import QueryProvider from "@/components/providers/QueryProvider";
import UserGate from "@/components/providers/UserGate";
import { Toaster } from "sonner";
import CookieConsentBanner from "@/components/cookie-consent-banner";
import ClarityProvider from "@/components/clarity-provider";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "Amplify",
    url: "https://www.boss.reviews",
  };

  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
        <script async src="https://tally.so/widgets/embed.js"></script>
        <script
          id="bmc-widget"
          data-name="BMC-Widget"
          data-cfasync="false"
          src="https://cdnjs.buymeacoffee.com/1.0.0/widget.prod.min.js"
          data-id="amplify"
          data-description="Support me on Buy me a coffee!"
          data-color="#BD5FFF"
          data-position="Right"
          data-x_margin="18"
          data-y_margin="18"
        ></script>
      </head>
      <body className={inter.className}>
        <ClarityProvider />
        <UserProvider>
          <QueryProvider>
            <div className="relative flex min-h-screen flex-col">
              <Header />
              <Toaster position="bottom-center" />
              <main className="flex-1">{children}</main>
              <CookieConsentBanner />
            </div>
          </QueryProvider>
        </UserProvider>
      </body>
    </html>
  );
}
