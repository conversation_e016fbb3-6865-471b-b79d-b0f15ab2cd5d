"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import NetworkAnalysisTab from "../components/NetworkAnalysisTab";
import OverviewTab from "../components/OverviewTab";
import SentimentTab from "../components/SentimentTab";
import ReviewsTab from "../components/ReviewsTab";
import CompetencyTab from "../components/CompetencyTab";
import { useSearchParams } from "next/navigation";
import BossAdminAutocomplete from "@/components/autocomplete-boss-admin";

const AdminDashboard: React.FC = () => {
  const searchParams = useSearchParams();
  const [selectedBossId, setSelectedBossId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Separate useEffect for URL parameter
  useEffect(() => {
    const bossId = searchParams.get("selectedBossId");
    if (bossId) {
      setSelectedBossId(bossId);
      // Scroll to reviews section after a short delay to ensure content is loaded
      setTimeout(() => {
        document
          .getElementById("reviews-section")
          ?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }
  }, [searchParams]);

  const handleBossSelect = (bossName: string, bossId: string) => {
    setSelectedBossId(bossId);
  };

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen text-red-500">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>

      <div className="mb-8">
        <BossAdminAutocomplete onSubmit={handleBossSelect} />
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
          <TabsTrigger value="competency">Competencies</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="network">Network</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <OverviewTab selectedBossId={selectedBossId} />
        </TabsContent>

        <TabsContent value="sentiment">
          <SentimentTab selectedBossId={selectedBossId} />
        </TabsContent>

        <TabsContent value="competency">
          <CompetencyTab selectedBossId={selectedBossId} />
        </TabsContent>

        <TabsContent value="reviews">
          <div id="reviews-section">
            <ReviewsTab selectedBossId={selectedBossId} />
          </div>
        </TabsContent>

        <TabsContent value="network">
          <NetworkAnalysisTab selectedBossId={selectedBossId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminDashboard;
