"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
  ChevronUp,
  ChevronDown,
  Trash2,
  RotateCcw,
  Loader2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Tit<PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import useDebounce from "@/hooks/useDebounce";

interface Boss {
  boss_id: string;
  name: string;
  deleted_at: string | null;
  stats: {
    total_reviews: number;
    average_nps: number;
    average_sentiment: number;
    companies: string[];
    status: "active" | "inactive" | "flagged";
  };
}

type SortField =
  | "name"
  | "total_reviews"
  | "average_nps"
  | "average_sentiment"
  | "status";
type SortOrder = "asc" | "desc";

type FilterStatus = "all" | "deleted" | "not-deleted";

const fetchBosses = async (
  bossName: string,
  filterStatus: FilterStatus,
): Promise<Boss[]> => {
  const supabase = createClient();

  // Build the query with filters
  let query = supabase
    .from("bosses")
    .select(
      `
      boss_id,
      name,
      deleted_at,
      reviews!reviews_boss_id_fkey (
        review_id,
        nps_score,
        status,
        companies!reviews_company_id_fkey (
          name
        ),
        sentiments!reviews_sentiment_id_fkey (
          sentiment
        )
      )
    `,
    )
    .ilike("name", `%${bossName}%`);

  // Apply deletion filter
  if (filterStatus === "deleted") {
    query = query.not("deleted_at", "is", null);
  } else if (filterStatus === "not-deleted") {
    query = query.is("deleted_at", null);
  }
  // For "all", no additional filter is needed

  const { data: bossesData, error: bossesError } = await query;

  if (bossesError) {
    console.error("Supabase error:", bossesError);
    throw new Error(bossesError.message || "Failed to fetch bosses data");
  }

  if (!bossesData) {
    throw new Error("No data returned from Supabase");
  }

  // Process the data to calculate statistics
  const processedBosses = bossesData.map((boss: any) => {
    const activeReviews = (boss.reviews || []).filter(
      (r: any) => r.status === "published" || r.status === "approved",
    );

    // Get unique companies
    const companies = new Set();
    boss.reviews?.forEach((review: any) => {
      if (review.companies?.name) {
        companies.add(review.companies.name);
      }
    });

    // Calculate NPS average
    const npsScores = activeReviews
      .map((r: any) => r.nps_score)
      .filter((score: any) => score !== null && score !== undefined);

    const averageNPS =
      npsScores.length > 0
        ? npsScores.reduce((a: number, b: number) => a + b, 0) /
          npsScores.length
        : 0;

    // Calculate sentiment average
    const sentiments = activeReviews
      .map((r: any) => r.sentiments?.[0]?.sentiment)
      .filter(
        (sentiment: any) => sentiment !== null && sentiment !== undefined,
      );

    const averageSentiment =
      sentiments.length > 0
        ? sentiments.reduce((a: number, b: number) => a + b, 0) /
          sentiments.length
        : 0;

    // Determine boss status
    let status: "active" | "inactive" | "flagged" = "inactive";
    if (activeReviews.length > 0) {
      status = "active";
      if (boss.reviews?.some((r: any) => r.status === "flagged")) {
        status = "flagged";
      }
    }

    return {
      boss_id: boss.boss_id,
      name: boss.name,
      deleted_at: boss.deleted_at,
      stats: {
        total_reviews: activeReviews.length,
        average_nps: averageNPS,
        average_sentiment: averageSentiment,
        companies: Array.from(companies) as string[],
        status,
      },
    };
  });

  // Sort by review count (descending) by default
  // This ensures bosses with the most reviews appear first
  return processedBosses.sort(
    (a, b) => b.stats.total_reviews - a.stats.total_reviews,
  );
};

const BossesManagementPage: React.FC = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [sortField, setSortField] = useState<SortField>("total_reviews");
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc");
  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all");

  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // State for delete/restore functionality
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedBoss, setSelectedBoss] = useState<Boss | null>(null);
  const [actionType, setActionType] = useState<"delete" | "restore">("delete");
  const [isUpdating, setIsUpdating] = useState(false);

  const {
    data: bosses = [],
    isLoading,
    error,
  } = useQuery<Boss[]>({
    queryKey: ["admin-bosses", debouncedSearchTerm, filterStatus],
    queryFn: () => fetchBosses(debouncedSearchTerm, filterStatus),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const handleFilterChange = (value: FilterStatus) => {
    setFilterStatus(value);
    // setCurrentPage(1);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Delete/Restore functions
  const handleDeleteClick = (boss: Boss, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent row click
    setSelectedBoss(boss);
    setActionType("delete");
    setShowConfirmDialog(true);
  };

  const handleRestoreClick = (boss: Boss, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent row click
    setSelectedBoss(boss);
    setActionType("restore");
    setShowConfirmDialog(true);
  };

  const handleConfirmAction = async () => {
    if (!selectedBoss) return;

    setIsUpdating(true);
    try {
      const supabase = createClient();
      const updateData =
        actionType === "delete"
          ? { deleted_at: new Date().toISOString() }
          : { deleted_at: null };

      const { error } = await supabase
        .from("bosses")
        .update(updateData)
        .eq("boss_id", selectedBoss.boss_id);

      if (error) throw error;

      // Invalidate and refetch the bosses query
      queryClient.invalidateQueries({ queryKey: ["admin-bosses"] });

      toast.success(
        `Boss ${actionType === "delete" ? "deleted" : "restored"} successfully`,
      );
    } catch (error) {
      console.error(`Error ${actionType}ing boss:`, error);
      toast.error(`Failed to ${actionType} boss`);
    } finally {
      setIsUpdating(false);
      setShowConfirmDialog(false);
      setSelectedBoss(null);
    }
  };

  const handleCancelAction = () => {
    setShowConfirmDialog(false);
    setSelectedBoss(null);
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("asc");
    }
  };

  const getSortedBosses = (bosses: Boss[]) => {
    const sortedBosses = [...bosses];

    sortedBosses.sort((a, b) => {
      let comparison = 0;

      switch (sortField) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "total_reviews":
          comparison = a.stats.total_reviews - b.stats.total_reviews;
          break;
        case "average_nps":
          comparison = a.stats.average_nps - b.stats.average_nps;
          break;
        case "average_sentiment":
          comparison = a.stats.average_sentiment - b.stats.average_sentiment;
          break;
        case "status":
          comparison = a.stats.status.localeCompare(b.stats.status);
          break;
      }

      return sortOrder === "asc" ? comparison : -comparison;
    });

    return sortedBosses;
  };

  // Get sorted bosses for display
  const sortedBosses = getSortedBosses(bosses);

  const SortableHeader: React.FC<{
    field: SortField;
    children: React.ReactNode;
  }> = ({ field, children }) => (
    <TableHead
      className="cursor-pointer hover:bg-muted/50 whitespace-nowrap"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center gap-1 sm:gap-2">
        <span className="text-xs sm:text-sm">{children}</span>
        {sortField === field &&
          (sortOrder === "asc" ? (
            <ChevronUp className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
          ) : (
            <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
          ))}
      </div>
    </TableHead>
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "flagged":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getNPSColor = (score: number) => {
    if (score >= 9) return "text-green-600";
    if (score >= 7) return "text-blue-600";
    if (score >= 4) return "text-yellow-600";
    return "text-red-600";
  };

  const handleBossClick = (bossId: string) => {
    router.push(`/admin/dashboard?selectedBossId=${bossId}`);
  };

  // Skeleton component for loading state
  const TableRowSkeleton = () => (
    <TableRow>
      <TableCell className="font-medium">
        <Skeleton className="h-4 w-24 sm:w-32" />
      </TableCell>
      <TableCell>
        <div className="flex flex-wrap gap-1">
          <Skeleton className="h-5 w-16 sm:h-6 sm:w-20" />
          <Skeleton className="h-5 w-20 sm:h-6 sm:w-24" />
        </div>
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-6 sm:w-8" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-8 sm:w-12" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-8 sm:w-12" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-5 w-12 sm:h-6 sm:w-16" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-5 w-10 sm:h-6 sm:w-12" />
      </TableCell>
      <TableCell>
        <div className="flex gap-1 sm:gap-2">
          <Skeleton className="h-7 w-7 sm:h-8 sm:w-8" />
        </div>
      </TableCell>
    </TableRow>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle>Boss Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex flex-col sm:flex-row gap-4 sm:justify-between sm:items-center">
            <Input
              type="text"
              placeholder="Search bosses or companies..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-full sm:max-w-sm"
            />
            <Tabs
              value={filterStatus}
              onValueChange={(value) =>
                handleFilterChange(value as FilterStatus)
              }
              className="w-full sm:w-auto"
            >
              <TabsList className="grid w-full grid-cols-3 sm:w-auto sm:grid-cols-none sm:flex">
                <TabsTrigger value="all" className="text-xs sm:text-sm">
                  All
                </TabsTrigger>
                <TabsTrigger value="deleted" className="text-xs sm:text-sm">
                  Deleted
                </TabsTrigger>
                <TabsTrigger value="not-deleted" className="text-xs sm:text-sm">
                  Active
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <SortableHeader field="name">Name</SortableHeader>
                  <TableHead className="whitespace-nowrap text-xs sm:text-sm">
                    Companies
                  </TableHead>
                  <SortableHeader field="total_reviews">Reviews</SortableHeader>
                  <SortableHeader field="average_nps">NPS</SortableHeader>
                  <SortableHeader field="average_sentiment">
                    Sentiment
                  </SortableHeader>
                  <SortableHeader field="status">Status</SortableHeader>
                  <TableHead className="whitespace-nowrap text-xs sm:text-sm">
                    Deleted
                  </TableHead>
                  <TableHead className="whitespace-nowrap text-xs sm:text-sm">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(10)
                    .fill(0)
                    .map((_, index) => <TableRowSkeleton key={index} />)
                ) : error ? (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      className="text-center text-red-500 py-8"
                    >
                      Error loading bosses: {error.message}
                    </TableCell>
                  </TableRow>
                ) : sortedBosses.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      className="text-center text-muted-foreground py-8"
                    >
                      No bosses found
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedBosses.map((boss) => (
                    <TableRow
                      key={boss.boss_id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleBossClick(boss.boss_id)}
                    >
                      <TableCell className="font-medium text-sm sm:text-base max-w-32 sm:max-w-none truncate">
                        {boss.name}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {boss.stats.companies
                            .slice(0, 2)
                            .map((company, index) => (
                              <Badge
                                key={index}
                                variant="outline"
                                className="text-xs"
                              >
                                {company}
                              </Badge>
                            ))}
                          {boss.stats.companies.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{boss.stats.companies.length - 2}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-sm sm:text-base">
                        {boss.stats.total_reviews}
                      </TableCell>
                      <TableCell
                        className={`text-sm sm:text-base ${getNPSColor(boss.stats.average_nps)}`}
                      >
                        {boss.stats.average_nps.toFixed(1)}
                      </TableCell>
                      <TableCell className="text-sm sm:text-base">
                        {boss.stats.average_sentiment.toFixed(2)}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={`text-xs ${getStatusColor(boss.stats.status)}`}
                        >
                          {boss.stats.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {boss.deleted_at ? (
                          <Badge
                            variant="outline"
                            className="bg-red-100 text-red-800 text-xs"
                          >
                            Deleted
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="bg-green-100 text-green-800 text-xs"
                          >
                            Active
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1 sm:gap-2">
                          {boss.deleted_at ? (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => handleRestoreClick(boss, e)}
                              className="h-7 w-7 sm:h-8 sm:w-8 p-0"
                              title="Restore boss"
                            >
                              <RotateCcw className="h-3 w-3 sm:h-4 sm:w-4" />
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => handleDeleteClick(boss, e)}
                              className="h-7 w-7 sm:h-8 sm:w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="Delete boss"
                            >
                              <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {actionType === "delete" ? "Delete Boss" : "Restore Boss"}
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to {actionType}{" "}
              <strong>{selectedBoss?.name}</strong>?
            </p>
            {actionType === "delete" && (
              <p className="text-sm text-muted-foreground mt-2">
                This will soft delete the boss. You can restore them later if
                needed.
              </p>
            )}
            {actionType === "restore" && (
              <p className="text-sm text-muted-foreground mt-2">
                This will restore the boss and make them visible again.
              </p>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleCancelAction}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              variant={actionType === "delete" ? "destructive" : "default"}
              onClick={handleConfirmAction}
              disabled={isUpdating}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {actionType === "delete" ? "Deleting..." : "Restoring..."}
                </>
              ) : actionType === "delete" ? (
                "Delete"
              ) : (
                "Restore"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BossesManagementPage;
