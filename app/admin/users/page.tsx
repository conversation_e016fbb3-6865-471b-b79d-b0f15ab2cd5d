"use client";

import React, { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import UserDetailsModal from "../dashboard/UserDetailsModal";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import useDebounce from "@/hooks/useDebounce";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ADMIN_ROLES_IDS } from "@/app/constants";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface UserData {
  user_id: string;
  email: string;
  role_id: number | null;
  created_at?: string;
  last_sign_in?: string;
  stats: {
    total_reviews: number;
    total_bosses: number;
    total_companies: number;
  };
}

type FilterStatus = "all" | "admins" | "users";

const UsersManagementPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 1000);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalUsersCount, setTotalUsersCount] = useState<number>(0);
  const pageSize = 10;

  const supabase = createClient();
  const queryClient = useQueryClient();

  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all");

  const fetchUsers = async (
    page: number,
    pageSize: number,
    searchTerm: string,
    filterStatus: FilterStatus,
  ) => {
    // Build the count query with filters
    let countQuery = supabase
      .from("users")
      .select("*", { count: "exact", head: true })
      .ilike("email", `%${searchTerm}%`);

    // Apply role filter for count
    if (filterStatus === "admins") {
      countQuery = countQuery.in("role_id", ADMIN_ROLES_IDS);
    } else if (filterStatus === "users") {
      countQuery = countQuery.is("role_id", null);
    }

    const { count } = await countQuery;
    setTotalUsersCount(count ?? 0);

    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    // Build the main query with filters
    let usersQuery = supabase
      .from("users")
      .select(
        `
        *,
        reviews:reviews!reviewer_id(count),
        boss_connections:reviews(
          boss_id,
          bosses(boss_id, name)
        ),
        company_connections:reviews(
          company_id,
          companies(company_id, name)
        ),
        saved_bosses(
          boss_id,
          bosses(boss_id, name)
        )
      `,
      )
      .ilike("email", `%${searchTerm}%`);

    // Apply role filter for main query
    if (filterStatus === "admins") {
      usersQuery = usersQuery.in("role_id", ADMIN_ROLES_IDS);
    } else if (filterStatus === "users") {
      usersQuery = usersQuery.is("role_id", null);
    }

    const { data: usersData, error: usersError } = await usersQuery
      .order("reviews_count", { ascending: false })
      .range(from, to);

    if (usersError) throw usersError;

    // Process the data to count unique bosses and companies
    const processedUsers = usersData?.map((user) => {
      const fullBossArray = [
        ...(user?.boss_connections || []),
        ...(user?.saved_bosses || []),
      ];
      const uniqueBosses = new Set(
        fullBossArray.map((conn: any) => conn.boss_id),
      );
      const uniqueCompanies = new Set(
        user.company_connections?.map((conn: any) => conn.company_id),
      );

      return {
        user_id: user.id,
        email: user.email,
        role_id: user.role_id,
        created_at: user.created_at,
        last_sign_in: user.last_sign_in,
        stats: {
          total_reviews: user.reviews[0]?.count || 0,
          total_bosses: uniqueBosses.size,
          total_companies: uniqueCompanies.size,
        },
      };
    });

    return processedUsers || [];
  };

  const updateUserRole = async (userId: string, roleId: number | null) => {
    try {
      const { error } = await supabase
        .from("users")
        .update({ role_id: roleId })
        .eq("id", userId);

      if (error) throw error;

      // Invalidate and refetch the users query
      queryClient.invalidateQueries({ queryKey: ["admin-users"] });

      toast.success(`User role updated successfully`);
    } catch (error) {
      console.error("Error updating user role:", error);
      toast.error("Failed to update user role");
    }
  };

  const getRoleDisplay = (roleId: number | null) => {
    if (roleId && ADMIN_ROLES_IDS.includes(roleId)) {
      return "Admin";
    }
    return "User";
  };

  const {
    data: users = [],
    isLoading,
    error,
  } = useQuery<UserData[]>({
    queryKey: [
      "admin-users",
      pageSize,
      currentPage,
      debouncedSearchTerm,
      filterStatus,
    ],
    queryFn: () =>
      fetchUsers(currentPage, pageSize, debouncedSearchTerm, filterStatus),
  });

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1); // Reset to first page on new search
  };

  const handleFilterChange = (value: FilterStatus) => {
    setFilterStatus(value);
    setCurrentPage(1); // Reset to first page on filter change
  };

  // Pagination logic
  const totalPages = Math.ceil(totalUsersCount / pageSize);

  const goToNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const goToPrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const TableRowSkeleton = () => (
    <TableRow>
      <TableCell className="font-medium text-transparent flex">
        <Skeleton className="h-4 w-32" />1{" "}
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-40" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-16" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-10" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-10" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-10" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-24" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-24" />
      </TableCell>
    </TableRow>
  );

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen text-red-500">
        <p>
          {error instanceof Error
            ? error.message
            : "An error occurred while fetching users"}
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle>User Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex justify-between items-center">
            <Input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={handleSearch}
              className="max-w-sm"
            />
            <Tabs
              value={filterStatus}
              onValueChange={(value) =>
                handleFilterChange(value as FilterStatus)
              }
            >
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="admins">Admins</TabsTrigger>
                <TabsTrigger value="users">Users</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User ID</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Reviews</TableHead>
                <TableHead>Bosses</TableHead>
                <TableHead>Companies</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading
                ? Array(pageSize)
                    .fill(0)
                    .map((_, index) => <TableRowSkeleton key={index} />)
                : users.map((user) => (
                    <TableRow key={user.user_id} className="hover:bg-muted/50">
                      <TableCell
                        className="font-medium max-w-28 text-nowrap overflow-hidden text-ellipsis cursor-pointer"
                        onClick={() => setSelectedUser(user.user_id)}
                      >
                        {user.user_id}
                      </TableCell>
                      <TableCell
                        className="cursor-pointer"
                        onClick={() => setSelectedUser(user.user_id)}
                      >
                        {user.email}
                      </TableCell>
                      <TableCell >
                        <Select
                          value={
                            user.role_id ? user.role_id.toString() : "null"
                          }
                          onValueChange={(value: string) => {
                            const roleId =
                              value === "null" ? null : parseInt(value);
                            updateUserRole(user.user_id, roleId);
                          }}
                        >
                          <SelectTrigger className="w-40">
                            <SelectValue>
                              {getRoleDisplay(user.role_id)}
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="null">User</SelectItem>
                            <SelectItem value="1">Admin</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell
                        className="cursor-pointer"
                        onClick={() => setSelectedUser(user.user_id)}
                      >
                        {user.stats.total_reviews}
                      </TableCell>
                      <TableCell
                        className="cursor-pointer"
                        onClick={() => setSelectedUser(user.user_id)}
                      >
                        {user.stats.total_bosses}
                      </TableCell>
                      <TableCell
                        className="cursor-pointer"
                        onClick={() => setSelectedUser(user.user_id)}
                      >
                        {user.stats.total_companies}
                      </TableCell>
                    </TableRow>
                  ))}
            </TableBody>
          </Table>

          {/* Pagination Controls */}
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {users.length > 0 ? (currentPage - 1) * pageSize + 1 : 0}{" "}
              to {Math.min(currentPage * pageSize, totalUsersCount)} of{" "}
              {totalUsersCount} users
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={goToPrevPage}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="sr-only">Previous Page</span>
              </Button>
              <div className="text-sm">
                Page {currentPage} of {totalPages || 1}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={goToNextPage}
                disabled={currentPage === totalPages || totalPages === 0}
              >
                <ChevronRight className="h-4 w-4" />
                <span className="sr-only">Next Page</span>
              </Button>
            </div>
          </div>

          {selectedUser && (
            <UserDetailsModal
              userId={selectedUser}
              onClose={() => setSelectedUser(null)}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UsersManagementPage;
