import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { createClient } from '@/utils/supabase/client';
import { Loader2 } from 'lucide-react';
import { Review, Sentiment } from '@/types/boss';

interface OverviewTabProps {
  selectedBossId: string | null;
}

interface OverviewData {
  totalReviews: number;
  averageNPSScore: number;
  sentimentDistribution: Record<string, number>;
  relationshipBreakdown: Record<string, {
    count: number;
    averageSentiment: number;
    npsScore: number;
  }>;
}

const OverviewTab: React.FC<OverviewTabProps> = ({ selectedBossId }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<OverviewData | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      const supabase = createClient();

      try {
        let reviewsQuery = supabase
          .from('reviews')
          .select('*');



        let sentimentsQuery = supabase
          .from('sentiments')
          .select('*');

        if (selectedBossId) {
          reviewsQuery = reviewsQuery.eq('boss_id', selectedBossId);
          sentimentsQuery = sentimentsQuery.eq('boss_id', selectedBossId);
        }


        const [
          { data: reviewsData, error: reviewsError },
          { data: sentimentsData, error: sentimentsError }
        ] = await Promise.all([
          reviewsQuery,
          sentimentsQuery
        ]);

        console.log("===> reviewsError", reviewsError)
        console.log("===> sentimentsError", sentimentsError)

        if (reviewsError) throw reviewsError;
        if (sentimentsError) throw sentimentsError;

        const reviews = reviewsData || [];
        const sentiments = sentimentsData || [];

        // Calculate overview metrics
        const totalReviews = reviews.length;
        const averageNPSScore = reviews.reduce((sum, review) => sum + (review.nps_score || 0), 0) / totalReviews || 0;

        // Calculate sentiment distribution
        const sentimentDistribution = sentiments.reduce((acc, sentiment) => {
          const category = getSentimentCategory(sentiment.sentiment);
          acc[category] = (acc[category] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        // Calculate relationship breakdown
        const relationshipBreakdown = reviews.reduce((acc, review) => {
          if (!acc[review.relationship]) {
            acc[review.relationship] = {
              count: 0,
              sentimentSum: 0,
              npsSum: 0
            };
          }
          
          const sentiment = sentiments.find(s => s.review_id === review.review_id);
          
          acc[review.relationship].count += 1;
          acc[review.relationship].sentimentSum += sentiment?.sentiment || 0;
          acc[review.relationship].npsSum += review.nps_score || 0;
          
          return acc;
        }, {} as Record<string, { count: number; sentimentSum: number; npsSum: number; }>);

        // Calculate averages for each relationship
        Object.keys(relationshipBreakdown).forEach(relationship => {
          const rel = relationshipBreakdown[relationship];
          relationshipBreakdown[relationship] = {
            count: rel.count,
            averageSentiment: rel.sentimentSum / rel.count,
            npsScore: rel.npsSum / rel.count
          };
        });

        setData({
          totalReviews,
          averageNPSScore,
          sentimentDistribution,
          relationshipBreakdown
        });

      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [selectedBossId]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 p-4">
        Error: {error}
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-muted-foreground p-4">
        No data available
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalReviews}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Average NPS Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.averageNPSScore.toFixed(1)}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Relationship Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <table className="w-full">
            <thead>
              <tr>
                <th className="text-left">Relationship</th>
                <th className="text-left">Count</th>
                <th className="text-left">Avg Sentiment</th>
                <th className="text-left">NPS Score</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(data.relationshipBreakdown).map(([relationship, stats]) => (
                <tr key={relationship}>
                  <td>{relationship}</td>
                  <td>{stats.count}</td>
                  <td>{stats.averageSentiment.toFixed(2)}</td>
                  <td>{stats.npsScore.toFixed(1)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Sentiment Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <table className="w-full">
            <thead>
              <tr>
                <th className="text-left">Category</th>
                <th className="text-left">Count</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(data.sentimentDistribution).map(([category, count]) => (
                <tr key={category}>
                  <td>{category}</td>
                  <td>{count}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </CardContent>
      </Card>
    </div>
  );
};

const getSentimentCategory = (sentiment: number): string => {
  if (sentiment <= -2) return "Very Negative";
  if (sentiment < 0) return "Negative";
  if (sentiment === 0) return "Neutral";
  if (sentiment <= 1) return "Positive";
  return "Very Positive";
};

export default OverviewTab;
