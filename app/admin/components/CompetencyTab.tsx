import React, { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { competencyCategories } from '@/app/boss/[id]/constants/dimensions';
import { calculateCompetencyScores } from '@/app/boss/[id]/utils/competencyCalculations';
import { Loader2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CompetencyTabProps {
  selectedBossId: string | null;
}

export default function CompetencyTab({ selectedBossId }: CompetencyTabProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [competencyScores, setCompetencyScores] = useState<Record<string, {
    totalScore: number;
    count: number;
    averageScore: number;
    byRelationship: Record<string, {
      totalScore: number;
      count: number;
      averageScore: number;
    }>;
  }>>({});
  const [selectedRelationship, setSelectedRelationship] = useState<string>('all');

  useEffect(() => {
    fetchCompetencyData();
  }, [selectedBossId]);

  const fetchCompetencyData = async () => {
    if (!selectedBossId) {
      setCompetencyScores({});
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);
    const supabase = createClient();

    try {
      // Fetch reviews with questionnaire responses
      const { data: reviews, error: reviewsError } = await supabase
        .from('reviews')
        .select(`
          *,
          questionnaire_responses (*)
        `)
        .eq('boss_id', selectedBossId)

      if (reviewsError) throw reviewsError;

      // Extract questionnaire responses from reviews
      const questionnaireResponses = reviews
        .flatMap(review => review.questionnaire_responses || [])
        .filter(Boolean);

      // Calculate competency scores
      const scores = calculateCompetencyScores(reviews, questionnaireResponses);
      
      console.log('Calculated competency scores:', {
        bossId: selectedBossId,
        reviewCount: reviews.length,
        responseCount: questionnaireResponses.length,
        scores
      });

      setCompetencyScores(scores);
    } catch (error) {
      console.error('Error fetching competency data:', error);
      setError(error instanceof Error ? error.message : 'An error occurred while fetching data');
    } finally {
      setIsLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    // Convert from -2 to +2 scale to 1-5 scale for color coding
    const rawScore = ((score + 2) * 1.25) + 1;
    if (rawScore >= 4) return "text-green-500";
    if (rawScore >= 3) return "text-yellow-500";
    return "text-red-500";
  };

  const formatScore = (score: number) => {
    // Show both normalized (-2 to +2) and raw (1-5) scores
    // const rawScore = Math.round(((score + 2) * 1.25) + 1);
    return `${score.toFixed(1)} (${score.toFixed(5)})`;
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64 text-red-500">
        <p>{error}</p>
      </div>
    );
  }

  if (!selectedBossId) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <p>Select a boss to view competency analysis</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  const relationships = Array.from(
    new Set(
      Object.values(competencyScores).flatMap(
        score => Object.keys(score.byRelationship)
      )
    )
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Competency Analysis</h2>
        <Select
          value={selectedRelationship}
          onValueChange={(value) => setSelectedRelationship(value)}
        >
          <SelectTrigger className="w-[200px]">
            <SelectValue defaultValue="all" placeholder="Select relationship" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Relationships</SelectItem>
            {relationships.map(rel => (
              <SelectItem key={rel} value={rel}>{rel}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Object.entries(competencyCategories).map(([category, { icon: Icon }]) => {
          const scores = competencyScores[category];
          const avgScore = selectedRelationship === 'all' 
            ? scores?.averageScore 
            : scores?.byRelationship[selectedRelationship]?.averageScore;
          const responseCount = selectedRelationship === 'all'
            ? scores?.count
            : scores?.byRelationship[selectedRelationship]?.count;

          return (
            <Card key={category}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {category}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  <span className={getScoreColor(avgScore || 0)}>
                    {formatScore(avgScore || 0)}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Based on {responseCount || 0} responses
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Detailed Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Competency</TableHead>
                <TableHead>Normalized (-2 to +2)</TableHead>
                <TableHead>Raw Score (1-5)</TableHead>
                <TableHead>Responses</TableHead>
                <TableHead>By Relationship</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Object.entries(competencyScores).map(([category, data]) => {
                // const rawScore = Math.round(((data.averageScore + 2) * 1.25) + 1);
                const rawScore = data.averageScore;

                return (
                  <TableRow key={category}>
                    <TableCell className="font-medium">{category}</TableCell>
                    <TableCell className={getScoreColor(data.averageScore)}>
                      {data.averageScore.toFixed(1)}
                    </TableCell>
                    <TableCell className={getScoreColor(data.averageScore)}>
                      {rawScore.toFixed(5)}
                    </TableCell>
                    <TableCell>{data.count}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {Object.entries(data.byRelationship).map(([rel, relData]) => {
                          const relRawScore = relData.averageScore
                          return (
                            <div key={rel} className="text-sm">
                              {rel}: <span className={getScoreColor(relData.averageScore)}>
                                {relData.averageScore.toFixed(1)} ({relRawScore})
                              </span>
                              <span className="text-muted-foreground"> ({relData.count})</span>
                            </div>
                          );
                        })}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <div className="flex flex-wrap justify-center gap-4 text-sm">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded bg-green-500" />
          <span className="text-muted-foreground">Good (≥4)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded bg-yellow-500" />
          <span className="text-muted-foreground">Average (3)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded bg-red-500" />
          <span className="text-muted-foreground">Needs Work (&lt;3)</span>
        </div>
      </div>
    </div>
  );
} 