import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');

  if (!query) {
    return NextResponse.json({ error: 'Query parameter is required' }, { status: 400 });
  }

  const supabase = createRouteHandlerClient({ cookies });

  const [{ data: bosses }, { data: companies }] = await Promise.all([
    supabase
      .from('bosses')
      .select('boss_id, name')
      .ilike('name', `%${query}%`)
      .is('deleted_at', null)
      .limit(10),
    supabase
      .from('companies')
      .select('company_id, name')
      .ilike('name', `%${query}%`)
      .limit(10)
  ]);

  return NextResponse.json({ bosses, companies });
}