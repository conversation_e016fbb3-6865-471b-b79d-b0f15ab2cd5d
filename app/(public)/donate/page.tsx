import DonatePage from "@/components/pages/donate-page";
import BuyMeCoffeeWidget from "@/components/buy-me-coffee-widget";

export const metadata = {
  title: "Support | Amplify",
  description: "Help Us Keep It Real",
  keywords: ["support", "donate", "company", "review", "amplify"],
  openGraph: {
    title: "Support | Amplify",
    description: "Help Us Keep It Real.",
    url: "https://www.boss.reviews/donate",
    siteName: "My Website",
    locale: "en_US",
    type: "website",
  },
  alternates: {
    canonical: "https://www.boss.reviews",
  },
};

const Page = () => (
  <>
    <DonatePage />
    <BuyMeCoffeeWidget />
  </>
);

export default Page