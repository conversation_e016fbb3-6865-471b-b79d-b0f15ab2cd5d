import AddBossPage from "@/components/pages/add-boss";

export const metadata = {
  title: "Add Boss | Amplify",
  description: "Add Boss to Amplify",
  keywords: ["add", "boss", "company", "review", "amplify"],
  openGraph: {
    title: "Add Boss | Amplify",
    description: "Help Us Keep It Real.",
    url: "https://www.boss.reviews/add-boss",
    siteName: "My Website",
    locale: "en_US",
    type: "website",
  },
  alternates: {
    canonical: "https://www.boss.reviews",
  },
};


const Page = () => <AddBossPage/>

export default Page