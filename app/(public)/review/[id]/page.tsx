import ReviewPage from "@/components/pages/review-page";
import { createClient } from "@/utils/supabase/client";
import type { QuestionnaireResponses } from "@/types/boss";
import type { Metadata } from "next";
import { notFound } from "next/navigation";

export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  const supabase = createClient();

  const { data: review, error } = await supabase
    .from("reviews")
    .select(
      `
      review_headline,
      review_content,
      bosses:boss_id!inner (
        name,
        deleted_at
      )
    `,
    )
    .eq("review_uuid", params.id)
    .is("bosses.deleted_at", null)
    .single();

  if (!review || error) {
    return {
      title: "Review Not Found | Amplify",
      description: "Review not found",
    };
  }

  const reviewTitle = review.review_headline || "Review";
  const bossName = review.bosses?.[0]?.name || "Unknown Boss";
  const contentSnippet =
    review.review_content?.split(" ").slice(0, 10).join(" ") || "";

  const title = `Review for ${bossName} - ${reviewTitle} - ${contentSnippet} | Amplify`;
  const description = `Read an honest review about ${bossName}.`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url: `https://www.boss.reviews/review/${params.id}`,
      siteName: "Amplify",
      locale: "en_US",
      type: "website",
    },
    alternates: {
      canonical: `https://www.boss.reviews/review/${params.id}`,
    },
  };
}

// 2. Page Component
const Page = async ({ params }: { params: { id: string } }) => {
  const { id } = params;

  const supabase = createClient();

  const { data: review, error } = await supabase
    .from("reviews")
    .select(
      `
      *,
      bosses:boss_id!inner (*),
      companies:company_id (*),
      users:reviewer_id (*),
      work_experiences:experience_id (*),
      sentiments:sentiment_id (*)
    `,
    )
    .eq("review_uuid", id)
    .is("bosses.deleted_at", null)
    .single();

  const { data: questionnaireResponses, error: questionnaireResponsesError } =
    await supabase
      .from("questionnaire_responses")
      .select("*")
      .eq("review_id", review?.review_id)
      .returns<QuestionnaireResponses[]>();

  const { data: reviewVotes } = await supabase
    .from("review_votes")
    .select("*")
    .eq("review_id", review?.review_id);

  let voteBalance = 0;
  if (reviewVotes) {
    const positive = reviewVotes.filter((v: any) => v.is_positive).length;
    const negative = reviewVotes.filter(
      (v: any) => v.is_positive === false,
    ).length;
    voteBalance = positive - negative;
  }

  if (error || questionnaireResponsesError || !review || !review.bosses || review.bosses.deleted_at) {
    notFound();
  }

  return (
    <ReviewPage
      review={review}
      questionnaireResponses={questionnaireResponses ?? []}
      voteBalance={voteBalance}
    />
  );
};

export default Page;
