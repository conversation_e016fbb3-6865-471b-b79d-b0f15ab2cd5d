import PrivacyPage from "@/components/pages/privacy-page";

export const metadata = {
  title: "Privacy Policy | Amplify",
  description: "Our commitment to your privacy and data protection",
  keywords: ["privacy", "policy", "data", "protection", "amplify"],
  openGraph: {
    title: "Privacy Policy | Amplify",
    description: "Our commitment to your privacy and data protection",
    url: "https://www.boss.reviews/privacy",
    siteName: "My Website",
    locale: "en_US",
    type: "website",
  },
  alternates: {
    canonical: "https://www.boss.reviews",
  },
};

const Page = () => <PrivacyPage />;

export default Page;
