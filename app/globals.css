@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 280 60% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 20% 97%;
    --secondary-foreground: 220 25% 20%;

    --muted: 220 15% 95%;
    --muted-foreground: 220 15% 45%;

    --accent: 220 25% 97%;
    --accent-foreground: 220 25% 20%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 15% 90%;
    --input: 220 15% 90%;
    --ring: 280 60% 50%;

    --radius: 0.75rem;

    --header-background: 0 0% 100%;
    --header-text: #7322A5;
    --header-hover: 277 66% 80%;
    --header-active: #7322A5;

    --drawer-background: 0 0% 100%;
    --drawer-overlay: 0 0% 0%;

    --z-header: 50;
    --z-sticky-section: 40;
    --z-drawer: 100;
    --z-dropdown: 50;
    --z-tooltip: 60;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-background text-foreground;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply tracking-tight font-semibold;
  }

  h1 {
    @apply text-2xl sm:text-3xl;
  }

  h2 {
    @apply text-xl sm:text-2xl;
  }

  h3 {
    @apply text-lg sm:text-xl;
  }
}

@layer components {
  /* Card Styles */
  .card {
    @apply bg-card text-card-foreground border border-border rounded-lg shadow-[0_2px_8px_rgba(0,0,0,0.04)];
  }

  /* Section Styles */
  .section {
    @apply py-16 sm:py-20;
  }

  .section-sm {
    @apply py-8 sm:py-12;
  }

  /* Container Styles */
  .container-lg {
    @apply max-w-6xl mx-auto px-4;
  }

  .container-md {
    @apply max-w-5xl mx-auto px-4;
  }

  /* Text Styles */
  .text-display {
    @apply text-4xl sm:text-5xl font-bold tracking-tight;
  }

  .text-title {
    @apply text-2xl sm:text-3xl font-semibold tracking-tight;
  }

  .text-subtitle {
    @apply text-lg sm:text-xl text-muted-foreground leading-relaxed;
  }

  .text-body {
    @apply text-base leading-relaxed;
  }

  .text-small {
    @apply text-sm text-muted-foreground;
  }

  /* Header Styles */
  .header {
    @apply bg-[hsl(var(--header-background))] text-[hsl(var(--header-text))];
    @apply backdrop-blur-sm backdrop-saturate-150;
    z-index: var(--z-header);
  }

  .header-link {
    @apply text-[hsl(var(--header-text))] hover:text-[hsl(var(--header-hover))] transition-colors duration-200;
  }

  .header-active {
    @apply text-[#C492E6];
  }

  /* Button Styles */
  .btn-icon {
    @apply inline-flex items-center justify-center;
  }

  .btn-icon-right {
    @apply ml-2 -mr-1 h-4 w-4;
  }

  .btn-icon-left {
    @apply -ml-1 mr-2 h-4 w-4;
  }

  /* Form Styles */
  .form-group {
    @apply space-y-2;
  }

  .input-label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  /* List Styles - Updated */
  .list-item {
    @apply flex items-center py-2;
  }

  .list-item-icon {
    @apply shrink-0 w-4 h-4 mr-3;
  }

  .list-item-text {
    @apply text-sm font-medium;
  }

  /* Grid Styles */
  .grid-cards {
    @apply grid gap-6 sm:grid-cols-2 lg:grid-cols-3;
  }

  /* Hover Effects */
  .hover-card {
    @apply hover:bg-secondary/50 transition-colors duration-200;
  }

  .hover-opacity {
    @apply hover:opacity-80 transition-opacity duration-200;
  }

  /* Navigation List Styles */
  .nav-item {
    @apply flex items-center gap-2 px-3 py-2 rounded-md transition-colors;
  }

  .nav-item-icon {
    @apply h-4 w-4;
  }

  .nav-item-text {
    @apply text-sm;
  }

  .nav-item-active {
    @apply bg-accent text-accent-foreground;
  }

  .nav-item-default {
    @apply text-muted-foreground hover:bg-accent hover:text-accent-foreground;
  }

  /* Update drawer styles */
  .drawer-content {
    @apply bg-[hsl(var(--drawer-background))] border-l shadow-lg;
    z-index: var(--z-drawer);
  }

  .drawer-overlay {
    @apply bg-black;
  }

  /* Drawer specific animations */
  @keyframes drawerSlideIn {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0%);
    }
  }

  @keyframes drawerSlideOut {
    from {
      transform: translateX(0%);
    }
    to {
      transform: translateX(100%);
    }
  }

  .drawer-content {
    animation: drawerSlideIn 700ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  .drawer-content[data-state="closed"] {
    animation: drawerSlideOut 700ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Update other sticky elements */
  .sticky-section {
    z-index: var(--z-sticky-section);
  }

  /* Accordion styles */
  .accordion-chevron[data-state="open"] {
    transform: rotate(180deg);
  }

  /* Custom accordion styles */
  [data-state="open"] > svg {
    transform: rotate(180deg);
  }
}

@layer utilities {
  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  /* Layout Utilities */
  .center {
    @apply flex items-center justify-center;
  }

  .center-between {
    @apply flex items-center justify-between;
  }

  /* Spacing Utilities */
  .stack {
    @apply space-y-4;
  }

  .stack-lg {
    @apply space-y-6;
  }

  .stack-sm {
    @apply space-y-2;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }

  @keyframes float-slow {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 5s ease-in-out infinite;
    animation-delay: 1s;
  }

  .animate-float-slow {
    animation: float-slow 7s ease-in-out infinite;
    animation-delay: 2s;
  }

  /* Hide scrollbar but maintain functionality */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

#bmc-wbtn {
  display: none !important;
}