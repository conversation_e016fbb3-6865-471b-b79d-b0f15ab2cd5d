import { NextResponse } from 'next/server'
import { createClient } from "@/utils/supabase/server";

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const next = searchParams.get('next') ?? '/'

  if (code) {
    const supabase = createClient()
    const { data: sessionData, error } = await supabase.auth.exchangeCodeForSession(code)

    if (!error && sessionData?.user) {
      const userId = sessionData.user.id

      const { data: userData, error: userError } = await supabase
          .from('users')
          .select('deleted_at')
          .eq('id', userId)
          .single()

      if (userError) {
        return NextResponse.redirect(`${origin}/auth/auth-code-error`)
      }

      if (userData?.deleted_at) {
        await supabase.auth.signOut()
        return NextResponse.redirect(`${origin}/auth/account-deleted`)
      }

      // If everything is OK, redirect to the specified path or profile
      const forwardedHost = request.headers.get('x-forwarded-host')

      // Use the next parameter directly if it's provided, otherwise default to /profile
      const redirectPath = next !== '/' ? next : '/profile'

       if (forwardedHost) {
         return NextResponse.redirect(`https://${forwardedHost}${redirectPath}`)
       } else {
         return NextResponse.redirect(`${origin}${redirectPath}`)
       }
    }
  }

  return NextResponse.redirect(`${origin}/auth/auth-code-error`)
}
