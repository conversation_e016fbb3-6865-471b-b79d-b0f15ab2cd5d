  "use client";

import React, { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import Link from "next/link";
import { Loader2, Building2, Users, Search, Star } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { useQuery } from "@tanstack/react-query";
import { useUser } from "@/contexts/UserContext";

interface ReviewedBoss {
  boss_id: number;
  name: string;
  nps_score: number | null;
  review_id: number;
}

interface CompanyWithBosses {
  company_id: number;
  name: string;
  industry: string | null;
  reviewed_bosses: ReviewedBoss[];
}

const fetchCompanies = async (userId: string) => {
  const supabase = createClient();

  const companyMap = new Map<number, CompanyWithBosses>();

  // 1. Fetch reviewed companies
  const { data: reviews, error: reviewError } = await supabase
    .from("reviews")
    .select(
      `
      review_id,
      nps_score,
      bosses!inner (
        boss_id,
        deleted_at,
        name,
        work_experiences (
          companies (
            company_id,
            name,
            industry
          )
        )
      )
    `,
    )
    .eq("reviewer_id", userId)
    .order("created_at", { ascending: false })
    .is("bosses.deleted_at", null)


  if (reviewError) throw new Error(reviewError.message);

  reviews?.forEach((review) => {
    const boss = review.bosses;
    const workExperiences = boss?.work_experiences || [];

    workExperiences.forEach((exp) => {
      const company = exp.companies;
      if (!company || !boss) return;

      if (!companyMap.has(company.company_id)) {
        companyMap.set(company.company_id, {
          company_id: company.company_id,
          name: company.name,
          industry: company.industry,
          reviewed_bosses: [],
        });
      }

      const existing = companyMap.get(company.company_id)!;

      if (!existing.reviewed_bosses.find((b) => b.boss_id === boss.boss_id)) {
        existing.reviewed_bosses.push({
          boss_id: boss.boss_id,
          name: boss.name,
          nps_score: review.nps_score,
          review_id: review.review_id,
        });
      }
    });
  });

  // Fetch companies from following bosses
  const { data: savedBosses, error: savedBossError } = await supabase
    .from("saved_bosses")
    .select(
      `
      boss_id,
      bosses!inner (
        boss_id,
        name,
        work_experiences (
          companies (
            company_id,
            name,
            industry
          )
        )
      )
    `,
    )
    .eq("user_id", userId)
    .is("bosses.deleted_at", null)


  if (savedBossError) throw new Error(savedBossError.message);

  savedBosses?.forEach((item) => {
    const boss = item.bosses;
    const workExperiences = boss?.work_experiences || [];

    workExperiences.forEach((exp) => {
      const company = exp.companies;
      if (!company || !boss) return;

      if (!companyMap.has(company.company_id)) {
        companyMap.set(company.company_id, {
          company_id: company.company_id,
          name: company.name,
          industry: company.industry,
          reviewed_bosses: [],
        });
      }
    });
  });

  return Array.from(companyMap.values());
};

const CompaniesPage = () => {
  const { user } = useUser();
  const [searchTerm, setSearchTerm] = useState("");

  const {
    data: companies,
    isLoading,
  } = useQuery({
    queryKey: ["user-companies", user?.id],
    queryFn: () => fetchCompanies(user!.id),
    enabled: !!user,
  });

  const filteredCompanies = companies?.filter(
    (company) =>
      company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      company.industry?.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">Companies</h1>
        <p className="text-muted-foreground">
          Companies where you've reviewed leaders
        </p>
      </div>

      <Separator />

      <div className="relative w-full sm:w-[300px]">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search companies..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-8"
        />
      </div>

      <div className="space-y-4">
        {filteredCompanies?.map((company) => (
          <div
            key={company.company_id}
            className="p-4 border rounded-lg bg-card shadow-sm"
          >
            <div className="flex flex-col md:flex-row justify-between items-start gap-4 md:gap-2">
              <div>
                <h3 className="font-medium text-md lg:text-lg">{company.name}</h3>
                {company.industry && (
                  <p className="text-sm text-muted-foreground">
                    {company.industry}
                  </p>
                )}
              </div>
              <div className="flex items-center gap-1 text-sm text-muted-foreground text-nowrap">
                <Users className="h-4 w-4" />
                <span>{company.reviewed_bosses.length} Leaders Reviewed</span>
              </div>
            </div>

            <div className="mt-4 space-y-1">
              {company.reviewed_bosses.map((boss) => (
                <Link
                  key={boss.boss_id}
                  href={`/boss/${boss.boss_id}`}
                  className="flex items-center justify-between p-2 rounded-md hover:bg-accent transition-colors"
                >
                  <span className="text-sm">{boss.name}</span>
                  {boss.nps_score !== null && (
                    <div className="flex items-center gap-1 text-sm">
                      <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                      <span>{boss.nps_score}</span>
                    </div>
                  )}
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>

      {filteredCompanies?.length === 0 && (
        <div className="text-center py-12">
          <Building2 className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
          <p className="text-muted-foreground mb-4">
            {searchTerm
              ? "No companies found matching your search."
              : "No companies yet. Start by reviewing a boss!"}
          </p>
        </div>
      )}
    </div>
  );
};

export default CompaniesPage;
