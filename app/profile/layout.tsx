"use client";

import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  Home,
  UserRoundPen,
  Settings,
  HelpCircle,
  ChevronRight,
  Building2,
  Star,
  Search,
  LogOut,
  Menu,
} from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { redirect } from "next/navigation";
import {routes} from "@/utils/routes";

interface LayoutProps {
  children: React.ReactNode;
}

const sidebarItems = [
  { icon: Home, label: "Overview", href: "/profile" },
  { icon: UserRoundPen, label: "My Reviews", href: "/profile/my-reviews" },
  { icon: Building2, label: "Companies", href: "/profile/companies" },
  { icon: Star, label: "Following", href: "/profile/following" },
  // { icon: Bell, label: 'Notifications', href: '/profile/notifications' },
  { icon: Settings, label: "Settings", href: "/profile/settings" },
];

export default function ProfileLayout({ children }: LayoutProps) {
  const [userName, setUserName] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [isMobileView, setIsMobileView] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const pathname = usePathname();

  // Add resize listener to detect mobile view
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 1024); // 1024px is the 'lg' breakpoint
    };

    // Set initial value
    if (typeof window !== "undefined") {
      handleResize();
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);

  useEffect(() => {
    const fetchUser = async () => {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      const { data: profile } = await supabase
        .from("profiles")
        .select("full_name")
        .eq("id", user.id)
        .single();

      setUserEmail(user.email || null);
      setUserName(profile?.full_name || user.email?.split("@")[0] || null);
    };

    fetchUser();
  }, []);

  const handleSignOut = async () => {
    const supabase = createClient();
    await supabase.auth.signOut();
    redirect("/");
  };

  return (
    <div className="min-h-[calc(100vh-4rem)] flex">
      {" "}
      {/* Subtract header height (4rem = 64px) */}
      {/* Fixed Sidebar - Desktop Only */}
      <aside className="hidden lg:block fixed left-0 top-16 bottom-0 w-[280px] overflow-y-auto border-r bg-background p-6 z-40">
        {/* User Profile Section */}
        <div className="mb-6 space-y-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback>
                {userName?.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="font-medium truncate">{userName}</p>
              <p className="text-sm text-muted-foreground truncate">
                {userEmail}
              </p>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-2 gap-2">
            <Button className="w-full text-nowrap" size="sm">
              <Link className={"text-nowrap flex justify-center items-center"} href={routes.findBoss}>
                <Search className="h-4 w-4 mr-2" />
                Find Boss
              </Link>
            </Button>
            <Button className="w-full" size="sm" variant="secondary">
              <Link className={"text-nowrap flex justify-center items-center"} href={routes.featured}>
                <UserRoundPen className="h-4 w-4 mr-2" />
                Review
              </Link>
            </Button>
          </div>
        </div>

        <Separator className="my-4" />

        {/* Navigation */}
        <nav className="space-y-1">
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`
                  flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium
                  transition-colors hover:bg-accent hover:text-accent-foreground
                  ${isActive ? "bg-accent text-accent-foreground" : "text-muted-foreground"}
                `}
                prefetch={true}
              >
                <item.icon className="h-4 w-4" />
                <span>{item.label}</span>
              </Link>
            );
          })}
        </nav>

        <Separator className="my-4" />

        {/* Help Section */}
        <div className="rounded-lg bg-accent/50 p-4">
          <div className="flex items-center gap-3">
            <HelpCircle className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Need Help?</p>
              <Button
                className="px-0"
                variant='link'
                data-tally-open="wop4KX"
                data-tally-emoji-text="👋"
                data-tally-emoji-animation="wave"
              >
                Visit Help Center →
              </Button>
            </div>
          </div>
        </div>
      </aside>
      {/* Main Content Area */}
      <main className="flex-1 ml-0 lg:ml-[280px] min-h-full relative pb-16 lg:pb-0 max-w-screen-2xl overflow-hidden">
        <div className="container px-2 md:px-4 py-6">
          {children}
        </div>
      </main>
      {/* Mobile Bottom Navigation */}
      {isMobileView && (
        <nav className="fixed bottom-0 left-0 right-0 bg-background border-t z-40 h-16">
          <div className="grid grid-cols-5 h-full">
            {sidebarItems.slice(0, 4).map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`
                    flex flex-col items-center justify-center gap-1
                    transition-colors
                    ${isActive ? "text-primary" : "text-muted-foreground"}
                  `}
                  prefetch={true}
                >
                  <item.icon className="h-5 w-5" />
                  <span className="text-xs">{item.label}</span>
                </Link>
              );
            })}

            {/* More Menu Button */}
            <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
              <SheetTrigger asChild>
                <button className="flex flex-col items-center justify-center gap-1 text-muted-foreground">
                  <Menu className="h-5 w-5" />
                  <span className="text-xs">More</span>
                </button>
              </SheetTrigger>
              <SheetContent side="bottom" className="h-[80vh] rounded-t-xl">
                <SheetHeader className="mb-4">
                  <SheetTitle>Menu</SheetTitle>
                </SheetHeader>

                {/* User Profile in Sheet */}
                <div className="flex items-center gap-3 mb-6">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>
                      {userName?.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="font-medium">{userName}</p>
                    <p className="text-sm text-muted-foreground">{userEmail}</p>
                  </div>
                </div>

                <Separator className="my-4" />

                {/* Additional Navigation Items */}
                <div className="space-y-4">
                  {sidebarItems.slice(4).map((item) => {
                    const isActive = pathname === item.href;
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={`
                          flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium
                          transition-colors hover:bg-accent hover:text-accent-foreground
                          ${isActive ? "bg-accent text-accent-foreground" : "text-muted-foreground"}
                        `}
                        onClick={() => setIsSheetOpen(false)}
                        prefetch={true}
                      >
                        <item.icon className="h-5 w-5" />
                        <span>{item.label}</span>
                      </Link>
                    );
                  })}

                  {/* Quick Actions */}
                  <div className="grid grid-cols-2 gap-2 mt-6">
                    <Button className="w-full" size="sm">
                      <Search className="h-4 w-4 mr-2" />
                      Find Boss
                    </Button>
                    <Button className="w-full" size="sm" variant="secondary">
                      <UserRoundPen className="h-4 w-4 mr-2" />
                      Review
                    </Button>
                  </div>

                  {/* Sign Out Button */}
                  <Button
                    variant="outline"
                    className="w-full mt-4"
                    onClick={handleSignOut}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </Button>

                  {/* Help Section */}
                  {/*<div className="rounded-lg bg-accent/50 p-4 mt-6">*/}
                  {/*  <div className="flex items-center gap-3">*/}
                  {/*    <HelpCircle className="h-5 w-5 text-muted-foreground" />*/}
                  {/*    <div>*/}
                  {/*      <p className="text-sm font-medium">Need Help?</p>*/}
                  {/*      <Link*/}
                  {/*        href="/help"*/}
                  {/*        className="text-xs text-primary hover:underline mt-1 inline-block"*/}
                  {/*        onClick={() => setIsSheetOpen(false)}*/}
                  {/*      >*/}
                  {/*        Visit Help Center →*/}
                  {/*      </Link>*/}
                  {/*    </div>*/}
                  {/*  </div>*/}
                  {/*</div>*/}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </nav>
      )}
    </div>
  );
}
