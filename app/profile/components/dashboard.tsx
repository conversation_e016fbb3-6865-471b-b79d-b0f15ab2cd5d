'use client';

import React, { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Star, Building2, UserRoundPen, ChevronRight,
  TrendingUp, ThumbsUp, Loader2
} from 'lucide-react';
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {useUser} from "@/contexts/UserContext";
import {
  useQuery,
} from '@tanstack/react-query'


const fetchDashboardData = async (userId: string) => {
  if (!userId) {
    throw new Error('User not authenticated');
  }

  const supabase = createClient();

  const [userReviewsResponse, userSavedBossesResponse] = await Promise.all([
    supabase
        .from('reviews')
        .select('boss_id')
        .eq('reviewer_id', userId)
        .order('created_at', { ascending: false }),

    supabase
        .from('saved_bosses')
        .select('boss_id')
        .eq('user_id', userId)
  ]);

  const userReviewedBosses = userReviewsResponse.data || [];
  const userSavedBossesData = userSavedBossesResponse.data || [];

  const reviewedBossIds = userReviewedBosses.map(review => review.boss_id);
  const savedBossIds = userSavedBossesData.map(saved => saved.boss_id);

  const uniqueBossIds = Array.from(new Set([...reviewedBossIds, ...savedBossIds]));

  if (uniqueBossIds.length === 0) {
    return {
      recentActivity: [],
      stats: {
        totalReviews: 0,
        companies: 0,
        impact: 0
      }
    };
  }

  const { data: allBossReviews, error } = await supabase
      .from('reviews')
      .select(`
      *,
      bosses:boss_id!inner(*),
      companies:company_id(*)
    `)
      .in('boss_id', uniqueBossIds)
      .is('bosses.deleted_at', null)
      .order('created_at', { ascending: false });

  if (error) {
    throw new Error(error.message);
  }

  const recentReviews = allBossReviews
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 5);

  const uniqueCompanies = new Set(allBossReviews.map(r => r.company_id));

  return {
    recentActivity: recentReviews,
    stats: {
      totalReviews: allBossReviews.length,
      companies: uniqueCompanies.size,
      impact: allBossReviews.reduce((acc, r) => acc + (r.helpful_count || 0), 0)
    }
  };
};



interface DashboardProps {
  userEmail: string | null;
}

export default function Dashboard({ userEmail }: DashboardProps) {


  const { user, isLoading: isLoadingUser } = useUser();

  const {
    data,
    isLoading,
  } = useQuery({
    queryKey: ['dashboard-overview-data', user?.id],
    queryFn: () => fetchDashboardData(user!.id),
    enabled: !!user && !isLoadingUser,
    staleTime: 1000 * 60 * 5
  });


  if (isLoading || isLoadingUser) {
      return (
          <div className="flex items-center justify-center min-h-[400px]">
              <Loader2 className="w-8 h-8 animate-spin"/>
          </div>
      );
  }

  return (
    <div className="mx-auto space-y-8">
      {/* Quick Stats */}
      <div className="hidden md:grid grid-cols-3 gap-8 ">
        <div className="flex items-center gap-3">
          <UserRoundPen className="h-5 w-5 min-w-5 text-primary" />
          <div className={"flex gap-2 items-center"}>
            <p className="text-2xl font-bold">{data?.stats.totalReviews}</p>
            <p className="text-sm text-muted-foreground">Reviews Written</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Building2 className="h-5 w-5 min-w-5 text-primary" />
          <div className={"flex gap-2 items-center"}>
            <p className="text-2xl font-bold">{data?.stats.companies}</p>
            <p className="text-sm text-muted-foreground">Companies</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <ThumbsUp className="h-5 w-5 min-w-5 text-primary" />
          <div className={"flex gap-2 items-center"}>
            <p className="text-2xl font-bold">{data?.stats.impact}</p>
            <p className="text-sm text-muted-foreground">Helpful Votes</p>
          </div>
        </div>
      </div>

      <Separator className="hidden md:block"/>

      {/* Recent Activity */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">Recent Activity</h2>
        </div>
        <div className="space-y-6">
          {data?.recentActivity.map((review) => (
            <Link
              key={review.review_id}
              href={`/boss/${review.boss_id}`}
              className="block hover:bg-accent/50 -mx-4 px-4 py-3 rounded-lg transition-colors"
            >
              <div className="flex items-start gap-4">
                <Avatar>
                  <AvatarFallback>
                    {review.bosses?.name?.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-medium truncate">{review.bosses?.name}</h3>
                    <div className="flex items-center gap-1 shrink-0">
                      <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                      <span>{review.nps_score}</span>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2 break-words">
                    {review.companies?.name}
                  </p>
                  {/* Review Content */}
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2 break-words">
                    {review.review_content}
                  </p>
                  <div className="flex items-center gap-2 text-xs">
                    <Badge variant="secondary">
                      {review.relationship}
                    </Badge>
                    <span className="text-muted-foreground">
                      {new Date(review.created_at).toLocaleDateString()}
                    </span>
                    {review.helpful_count > 0 && (
                      <span className="text-muted-foreground flex items-center gap-1">
                        <ThumbsUp className="h-3 w-3" />
                        {review.helpful_count}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>
    </div>
  );
}