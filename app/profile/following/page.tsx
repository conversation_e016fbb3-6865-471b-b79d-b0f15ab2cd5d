"use client";

import React from "react";
import { createClient } from "@/utils/supabase/client";
import { useUser } from "@/contexts/UserContext";
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";

const fetchSavedBosses = async (savedBosses: any[]) => {
  if (savedBosses.length === 0) return [];
  const savedBossesIds = savedBosses.map((boss) => boss.boss_id);
  const supabase = createClient();
  const { data, error } = await supabase
    .from("bosses")
    .select("*")
    .in("boss_id", savedBossesIds)
    .is("deleted_at", null);

  if (error) {
    throw new Error(error.message);
  }

  return data || [];
};

const Saved = () => {
  const { savedBosses, isLoading: isLoadingUser } = useUser();

  const { data: bosses = [], isLoading } = useQuery({
    queryKey: ["user-following-bosses", savedBosses],
    queryFn: () => fetchSavedBosses(savedBosses),
    enabled: savedBosses.length > 0,
  });

  if (isLoading || isLoadingUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">
          Following bosses
        </h1>
        <p className="text-muted-foreground">
          Bosses you're following for later reference
        </p>
      </div>

      <Separator />

      {/* Main Content */}
      <div className="space-y-6">
        {bosses.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-muted-foreground text-lg">
              You’re not following any bosses yet.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {bosses.map((boss) => (
              <div
                key={boss.boss_id}
                className="flex items-start gap-4 p-4 border rounded-lg bg-card shadow-sm"
              >
                {boss.image_url && (
                  <div className="w-32 h-32 flex-shrink-0 overflow-hidden rounded-md">
                    <img
                      src={boss.image_url}
                      alt={boss.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <div className="flex-1">
                  <h3 className="text-md lg:text-lg font-semibold">
                    {boss.name}
                  </h3>
                  <div className="mt-2">
                    <Link
                      href={`/boss/${boss.boss_id}`}
                      className="text-primary hover:text-primary/80 font-medium"
                    >
                      View details →
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Saved;
