"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { headers, cookies } from "next/headers";
import { redirect } from "next/navigation";

export const checkThatEmailAvailable = async (email: string) => {
  const supabase = createClient();

  const { data: users, error: fetchError } = await supabase
      .from('users')
      .select('id')
      .eq('email', email);

  if (fetchError) {
    return { error: "Something went wrong" };
  }

  if (users.length > 0) {
    return { error: 'This user already exist' };
  }
}


export const signUpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const supabase = createClient();
  const origin = headers().get("origin");

  if (!email || !password) {
    return { error: "Email and password are required" };
  }

  const { error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback`,
    },
  });

  if (error) {
    console.error(error.code + " " + error.message);
    return encodedRedirect("error", "/sign-up", error.message);
  } else {
    return encodedRedirect(
      "success",
      "/sign-up",
      "Thanks for signing up! Please check your email for a verification link.",
    );
  }
};

export async function signInAction(formData: FormData) {
  const email = formData.get('email')?.toString();
  const password = formData.get('password')?.toString();
  const redirectTo = formData.get('redirect')?.toString();
  const bossId = formData.get('bossId')?.toString();

  if (!email || !password) {
    return { error: "Email and password are required" };
  }

  const cookieStore = cookies();
  const supabase = createClient(cookieStore);

  const { error, data } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return { error: error.message };
  }

  // Check if user exists and is not deleted
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('deleted_at')
    .eq('id', data.user.id)
    .single();

  if (userError) {
    await supabase.auth.signOut();
    return { error: "Failed to fetch user data" };
  }

  if (userData?.deleted_at) {
    await supabase.auth.signOut();
    return { error: "This user was deleted" };
  }

  // If we have a redirect URL, use that
  if (redirectTo) {
    throw redirect(redirectTo);
  }

  // If no redirect URL but we have a bossId, go back to that boss page
  if (bossId) {
    throw redirect(`/boss/${bossId}`);
  }

  // Default fallback to profile
  throw redirect('/profile');
}

export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();

  const supabase = createClient();
  const origin = (await headers()).get("origin");
  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "Email is required");
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/reset-password`,
  });

  if (error) {
    return {"error": "Could not reset password."};
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return {"success": "Check your email for a link to reset your password."};
};

export const resetPasswordAction = async (password: string, confirmPassword: string, token: string) => {
  const supabase = createClient();

  if (!password || !confirmPassword) {
    return { success: false, error: "Password and confirm password are required" };
  }

  if (password !== confirmPassword) {
    return { success: false, error: "Passwords do not match" };
  }

  const { error: verifyError } = await supabase.auth.verifyOtp({
    token_hash: token,
    type: 'recovery',
  });


  if (verifyError) {
    return { success: false, error: "Invalid or expired token" };
  }

  const { error: updateError } = await supabase.auth.updateUser({ password });

  if (updateError) {
    if (updateError.code === 'same_password') {
      await supabase.auth.signOut();
      return { success: true };
    }
    return { success: false, error: "Password update failed" };
  }

  await supabase.auth.signOut();

  return { success: true };
};

export const signOutAction = async () => {
  const supabase = createClient();
  await supabase.auth.signOut();
  return redirect("/sign-in");
};

export const getBossesIds = async () => {
  const supabase =  createClient();
  const {data} = await supabase.from('bosses').select("boss_id").is('deleted_at', null)
  return data ?? []
}


type BossFilter =
  | 'top-rated'
  | 'trending'
  | 'communicator'
  | 'team'
  | 'growth'
  | 'strategic'
  | 'wildcard'
  | 'red-flags'
  | string;

type Review = {
  review_id: string;
  review_content: string;
  nps_score: number;
  relationship: string;
  created_at: string;
  status: string;
  sentiments: Array<{ sentiment_id: string; sentiment: number }>;
  company: { name: string };
};

type Boss = {
  boss_id: string;
  name: string;
  reviews: Review[];
};

type ProcessedBoss = {
  boss_id: string;
  name: string;
  average_rating: number;
  review_count: number;
  latest_review: {
    review_id: string;
    content: string;
    nps_score: number;
    relationship: string;
    sentiment: number;
    company_name: string;
  } | null;
};

export const getFeaturedBosses = async (filter?: BossFilter): Promise<ProcessedBoss[]> => {
  const supabase = createClient();

  // Define filter value mapping
  const filterValueMap: Record<string, { field: string; ascending: boolean }> = {
    'top-rated': { field: 'top_rated_value', ascending: false },
    'trending': { field: 'trending_value', ascending: false },
    'communicator': { field: 'communicators_value', ascending: false },
    'team': { field: 'team_value', ascending: false },
    'growth': { field: 'growth_value', ascending: false },
    'strategic': { field: 'strategic_value', ascending: false },
    'wildcard': { field: 'wild_cards_value', ascending: false },
    'red-flags': { field: 'top_rated_value', ascending: true },
  };

  // Default to top-rated if filter is not provided or not in the map
  const { field, ascending } = filterValueMap[filter || ''] || filterValueMap['top-rated'];

  // Common select query for all filters
  const selectQuery = `
    boss_id,
    name,
    boss_featured_calculation!boss_featured_calculation_boss_uuid_fkey(${field}),
    reviews (
      review_id,
      review_content,
      nps_score,
      relationship,
      created_at,
      status,
      sentiments!reviews_sentiment_id_fkey (
        sentiment_id,
        sentiment
      ),
      company:company_id (
        name
      )
    )
  `;

  // Execute query
  const { data, error } = await supabase
    .from("bosses")
    .select(selectQuery)
    .is('deleted_at', null)
    .order(`boss_featured_calculation(${field})`, { ascending })
    .limit(12);

  if (error) {
    console.error("Error fetching featured bosses:", error);
    return [];
  }

  // Process the data to calculate average ratings and format for display
  return (data as unknown as Boss[])?.map((boss) => {
    const reviews = boss.reviews || [];

    // Calculate average NPS score
    const totalNps = reviews.reduce(
      (sum, review) => sum + (review.nps_score || 0),
      0
    );
    const avgNps = reviews.length > 0 ? totalNps / reviews.length : 0;

    // Get the most recent review with status published or approved
    const latestReview = [...reviews]
      .filter(review => review.status === 'published' || review.status === 'approved')
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];

    return {
      boss_id: boss.boss_id,
      name: boss.name,
      average_rating: parseFloat(avgNps.toFixed(1)),
      review_count: reviews.length,
      latest_review: latestReview
        ? {
            review_id: latestReview.review_id,
            content: latestReview.review_content,
            nps_score: latestReview.nps_score,
            relationship: latestReview.relationship,
            sentiment: latestReview.sentiments?.[0]?.sentiment || 0,
            company_name: latestReview.company?.name || "Unknown Company",
          }
        : null,
    };
  }) || [];
};


export const getFeaturedTotalStats = async () => {
  const supabase = createClient();

  const [
    { count: reviewsCount, error: reviewsError },
    { count: bossesCount, error: bossesError },
    { count: companiesCount, error: companiesError },
    { data: npsScores, error: npsError }
  ] = await Promise.all([
    supabase.from('reviews').select('*', { count: 'exact', head: true }),
    supabase.from('bosses').select('*', { count: 'exact', head: true }),
    supabase.from('companies').select('*', { count: 'exact', head: true }),
    supabase.from('reviews').select('nps_score').not('nps_score', 'is', null)
  ]);

  if (reviewsError || bossesError || companiesError || npsError) {
    console.error('Error fetching data', { reviewsError, bossesError, companiesError, npsError });
    return {
      reviewsCount: 0,
      bossesCount: 0,
      companiesCount: 0,
      averageNpsScore: 0
    };
  }

  // Calculate average NPS score
  let averageNpsScore = 0;
  if (npsScores && npsScores.length > 0) {
    const totalScore = npsScores.reduce((sum, review) => sum + (review.nps_score || 0), 0);
    averageNpsScore = parseFloat((totalScore / npsScores.length).toFixed(1));
  }

  return {
    reviewsCount: reviewsCount ?? 0,
    bossesCount: bossesCount ?? 0,
    companiesCount: companiesCount ?? 0,
    averageNpsScore,
  };
}
