import { useInfiniteQuery } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";
import { useUser } from "@/contexts/UserContext";

// Custom interface for transformed review data
export interface TransformedReview {
  review_id: number;
  boss_id: number;
  boss_name: string;
  company_name: string;
  nps_score: number | null;
  review_headline: string | null;
  review_content: string | null;
  created_at: string;
  relationship: string | null;
  status: "pending" | "approved" | "published" | "flagged" | "hidden" | null;
  questionnaire_completed: boolean;
  helpful_count: number;
  questionnaire_responses?: Array<{
    question_id: string;
    score: number;
  }>;
}

// Stats interface
export interface ReviewStats {
  totalReviews: number;
  companies: number;
  impact: number;
}

const fetchReviewsWithPagination = async (
  userId: string,
  limit: number = 10,
  offset: number = 0,
) => {
  const supabase = createClient();

  const { data, error, count } = await supabase
    .from("reviews")
    .select(
      `
      *,
      bosses!inner (
        boss_id,
        name,
        deleted_at
      ),
      companies (
        company_id,
        name
      ),
      questionnaire_responses (
        question_id,
        score
      )
    `,
      { count: "exact" },
    )
    .eq('reviewer_id', userId)
    .order("created_at", { ascending: false })
    .is("bosses.deleted_at", null)
    .range(offset, offset + limit - 1);

  if (error) {
    throw new Error(`Error fetching reviews: ${error.message}`);
  }

  // Transform the data
  const transformedData = data.map(
    (review): TransformedReview => ({
      review_id: review.review_id,
      boss_id: review.boss_id,
      boss_name: review.bosses?.name || "Unknown Boss",
      company_name: review.companies?.name || "Unknown Company",
      nps_score: review.nps_score,
      review_content: review.review_content,
      review_headline: review.review_headline,
      created_at: review.created_at,
      relationship: review.relationship,
      status: review.status || "pending",
      questionnaire_completed: review.questionnaire_completed || false,
      helpful_count: review.helpful_count || 0,
      questionnaire_responses: review.questionnaire_responses,
    }),
  );

  // Calculate stats
  const uniqueCompanies = new Set(data.map((r) => r.company_id));
  const totalHelpfulVotes = data.reduce(
    (sum, r) => sum + (r.helpful_count || 0),
    0,
  );

  const stats: ReviewStats = {
    totalReviews: count || 0,
    companies: uniqueCompanies.size,
    impact: totalHelpfulVotes,
  };

  return {
    reviews: transformedData,
    stats,
    totalCount: count || 0,
  };
};

// Hook for fetching reviews with filtering and pagination
export function useReviews(
  filter: "all" | "published" | "pending" = "all",
  limit: number = 10,
  offset: number = 0,
) {
  const { user } = useUser();

  // Define the return type of fetchReviewsWithPagination for type safety
  type ReviewsResponse = {
    reviews: TransformedReview[];
    stats: ReviewStats;
    totalCount: number;
  };

  const {
    data,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery<ReviewsResponse>({
    queryKey: ["my-reviews", user?.id, filter],
    queryFn: ({ pageParam }) => {
      // Only execute if user exists (this is a safeguard, as enabled: !!user should prevent this from running)
      if (!user) throw new Error("User is not authenticated");
      return fetchReviewsWithPagination(user.id, limit, pageParam as number);
    },
    getNextPageParam: (lastPage, allPages) => {
      const nextOffset = allPages.length * limit;
      return nextOffset < lastPage.totalCount ? nextOffset : undefined;
    },
    initialPageParam: 0,
    enabled: !!user,
    staleTime: 1000 * 60 * 5,
  });

  // Combine all pages of reviews
  const allReviews = data?.pages.flatMap((page) => page.reviews) || [];

  // Apply filters
  const filteredReviews = allReviews.filter((review) => {
    if (filter === "all") return true;
    if (filter === "published")
      return review.status === "approved" || review.status === "published";
    if (filter === "pending") return !review.questionnaire_completed;
    return true;
  });

  // Get stats from the first page
  const stats = data?.pages[0]?.stats || {
    totalReviews: 0,
    companies: 0,
    impact: 0,
  };

  // Calculate if there are more reviews to load
  const hasMore = hasNextPage || false;

  // Calculate total filtered count for accurate pagination
  const totalFilteredCount = data?.pages[0]?.totalCount || 0;

  return {
    reviews: filteredReviews,
    stats,
    isLoading,
    error,
    loadMore: fetchNextPage,
    hasMore,
    isLoadingMore: isFetchingNextPage,
    totalCount: totalFilteredCount,
  };
}
