#!/usr/bin/env python3
"""
Git Statistics Analyzer
Analyzes git commit logs to generate developer working pattern statistics
"""

import subprocess
import re
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import json

def get_git_log_data():
    """Get git log data with date and author information"""
    try:
        result = subprocess.run(
            ['git', 'log', '--pretty=format:%ad %an', '--date=short'],
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip().split('\n')
    except subprocess.CalledProcessError as e:
        print(f"Error running git command: {e}")
        return []

def normalize_author_name(author):
    """Normalize author names to handle variations"""
    # Map known variations to canonical names
    name_mapping = {
        'Oleksii': 'Oleksii Stupak',
        'Oleks<PERSON> Stupak': 'Oleks<PERSON> Stupak',
        'comalex': '<PERSON><PERSON><PERSON> Stupak',
        '<PERSON> Sid<PERSON>nko': '<PERSON>',
        '<PERSON>': '<PERSON>'
    }
    return name_mapping.get(author, author)

def analyze_git_logs():
    """Analyze git logs and generate statistics"""
    log_lines = get_git_log_data()
    
    if not log_lines or log_lines == ['']:
        print("No git log data found")
        return {}
    
    # Data structures for analysis
    daily_commits = defaultdict(lambda: defaultdict(int))  # date -> author -> count
    author_stats = defaultdict(lambda: {
        'total_commits': 0,
        'active_days': set(),
        'first_commit': None,
        'last_commit': None
    })
    
    # Parse each log line
    for line in log_lines:
        if not line.strip():
            continue
            
        parts = line.split(' ', 1)
        if len(parts) != 2:
            continue
            
        date_str, author = parts
        author = normalize_author_name(author.strip())
        
        try:
            commit_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            continue
        
        # Update statistics
        daily_commits[commit_date][author] += 1
        author_stats[author]['total_commits'] += 1
        author_stats[author]['active_days'].add(commit_date)
        
        # Track first and last commit dates
        if author_stats[author]['first_commit'] is None or commit_date < author_stats[author]['first_commit']:
            author_stats[author]['first_commit'] = commit_date
        if author_stats[author]['last_commit'] is None or commit_date > author_stats[author]['last_commit']:
            author_stats[author]['last_commit'] = commit_date
    
    return daily_commits, author_stats

def generate_statistics_report(daily_commits, author_stats):
    """Generate comprehensive statistics report"""
    report = []
    report.append("=" * 80)
    report.append("GIT REPOSITORY DEVELOPER ACTIVITY STATISTICS")
    report.append("=" * 80)
    report.append(f"Analysis generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Overall summary
    total_commits = sum(sum(authors.values()) for authors in daily_commits.values())
    total_active_days = len(daily_commits)
    total_authors = len(author_stats)
    
    report.append("OVERALL SUMMARY")
    report.append("-" * 40)
    report.append(f"Total commits: {total_commits}")
    report.append(f"Total active days: {total_active_days}")
    report.append(f"Total contributors: {total_authors}")
    report.append("")
    
    # Author statistics
    report.append("DEVELOPER STATISTICS")
    report.append("-" * 40)
    
    # Sort authors by total commits (descending)
    sorted_authors = sorted(author_stats.items(), key=lambda x: x[1]['total_commits'], reverse=True)
    
    for author, stats in sorted_authors:
        active_days_count = len(stats['active_days'])
        avg_commits_per_day = stats['total_commits'] / active_days_count if active_days_count > 0 else 0
        
        report.append(f"Author: {author}")
        report.append(f"  Total commits: {stats['total_commits']}")
        report.append(f"  Active days: {active_days_count}")
        report.append(f"  Average commits per active day: {avg_commits_per_day:.2f}")
        report.append(f"  First commit: {stats['first_commit']}")
        report.append(f"  Last commit: {stats['last_commit']}")
        
        if stats['first_commit'] and stats['last_commit']:
            period = (stats['last_commit'] - stats['first_commit']).days + 1
            report.append(f"  Active period: {period} days")
            if period > 0:
                activity_rate = (active_days_count / period) * 100
                report.append(f"  Activity rate: {activity_rate:.1f}% of days in period")
        report.append("")
    
    # Daily activity breakdown
    report.append("DAILY ACTIVITY BREAKDOWN")
    report.append("-" * 40)
    
    # Sort dates in descending order (most recent first)
    sorted_dates = sorted(daily_commits.keys(), reverse=True)
    
    for date in sorted_dates:
        authors_on_date = daily_commits[date]
        total_commits_on_date = sum(authors_on_date.values())
        
        report.append(f"{date} ({date.strftime('%A')}) - {total_commits_on_date} commits")
        
        # Sort authors by commits on this date
        sorted_authors_date = sorted(authors_on_date.items(), key=lambda x: x[1], reverse=True)
        for author, commits in sorted_authors_date:
            report.append(f"  {author}: {commits} commits")
        report.append("")
    
    # Weekly patterns
    report.append("WEEKLY PATTERNS")
    report.append("-" * 40)
    
    weekday_stats = defaultdict(lambda: defaultdict(int))
    for date, authors in daily_commits.items():
        weekday = date.strftime('%A')
        for author, commits in authors.items():
            weekday_stats[weekday][author] += commits
    
    weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    for weekday in weekdays:
        if weekday in weekday_stats:
            total_commits = sum(weekday_stats[weekday].values())
            report.append(f"{weekday}: {total_commits} commits")
            for author, commits in sorted(weekday_stats[weekday].items(), key=lambda x: x[1], reverse=True):
                report.append(f"  {author}: {commits} commits")
        else:
            report.append(f"{weekday}: 0 commits")
        report.append("")
    
    # Monthly patterns
    report.append("MONTHLY PATTERNS")
    report.append("-" * 40)
    
    monthly_stats = defaultdict(lambda: defaultdict(int))
    for date, authors in daily_commits.items():
        month_key = date.strftime('%Y-%m')
        for author, commits in authors.items():
            monthly_stats[month_key][author] += commits
    
    for month in sorted(monthly_stats.keys(), reverse=True):
        total_commits = sum(monthly_stats[month].values())
        report.append(f"{month}: {total_commits} commits")
        for author, commits in sorted(monthly_stats[month].items(), key=lambda x: x[1], reverse=True):
            report.append(f"  {author}: {commits} commits")
        report.append("")
    
    return "\n".join(report)

def main():
    """Main function to run the analysis"""
    print("Analyzing git repository...")
    
    daily_commits, author_stats = analyze_git_logs()
    
    if not daily_commits:
        print("No commit data found to analyze")
        return
    
    # Generate report
    report = generate_statistics_report(daily_commits, author_stats)
    
    # Save to file
    output_file = "git_developer_statistics.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"Statistics report saved to: {output_file}")
    print("\nPreview of the report:")
    print("-" * 50)
    # Show first 50 lines of the report
    lines = report.split('\n')
    for line in lines[:50]:
        print(line)
    
    if len(lines) > 50:
        print(f"\n... and {len(lines) - 50} more lines in the full report file.")

if __name__ == "__main__":
    main()
