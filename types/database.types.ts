export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      boss_featured_calculation: {
        Row: {
          boss_uuid: string | null
          communicators_value: number | null
          created_at: string
          growth_value: number | null
          id: number
          red_flags_value: number | null
          strategic_value: number | null
          team_value: number | null
          top_rated_value: number | null
          trending_value: number | null
          wild_cards_value: number | null
        }
        Insert: {
          boss_uuid?: string | null
          communicators_value?: number | null
          created_at?: string
          growth_value?: number | null
          id?: number
          red_flags_value?: number | null
          strategic_value?: number | null
          team_value?: number | null
          top_rated_value?: number | null
          trending_value?: number | null
          wild_cards_value?: number | null
        }
        Update: {
          boss_uuid?: string | null
          communicators_value?: number | null
          created_at?: string
          growth_value?: number | null
          id?: number
          red_flags_value?: number | null
          strategic_value?: number | null
          team_value?: number | null
          top_rated_value?: number | null
          trending_value?: number | null
          wild_cards_value?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "boss_featured_calculation_boss_uuid_fkey"
            columns: ["boss_uuid"]
            isOneToOne: true
            referencedRelation: "bosses"
            referencedColumns: ["boss_id"]
          },
        ]
      }
      bosses: {
        Row: {
          boss_id: string
          created_at: string
          deleted_at: string | null
          is_claimed: boolean | null
          linkedin_url: string | null
          name: string
          user_id: number | null
        }
        Insert: {
          boss_id?: string
          created_at?: string
          deleted_at?: string | null
          is_claimed?: boolean | null
          linkedin_url?: string | null
          name: string
          user_id?: number | null
        }
        Update: {
          boss_id?: string
          created_at?: string
          deleted_at?: string | null
          is_claimed?: boolean | null
          linkedin_url?: string | null
          name?: string
          user_id?: number | null
        }
        Relationships: []
      }
      companies: {
        Row: {
          company_id: string
          founded: string | null
          industry: string | null
          is_custom: boolean
          linkedin_url: string | null
          location: string | null
          name: string
        }
        Insert: {
          company_id?: string
          founded?: string | null
          industry?: string | null
          is_custom?: boolean
          linkedin_url?: string | null
          location?: string | null
          name: string
        }
        Update: {
          company_id?: string
          founded?: string | null
          industry?: string | null
          is_custom?: boolean
          linkedin_url?: string | null
          location?: string | null
          name?: string
        }
        Relationships: []
      }
      draft_reviews: {
        Row: {
          boss_id: string | null
          current_step: number | null
          draft_id: number
          expires_at: string | null
          form_data: Json | null
          furthest_step: number | null
          last_updated: string | null
          reviewer_id: string | null
        }
        Insert: {
          boss_id?: string | null
          current_step?: number | null
          draft_id?: number
          expires_at?: string | null
          form_data?: Json | null
          furthest_step?: number | null
          last_updated?: string | null
          reviewer_id?: string | null
        }
        Update: {
          boss_id?: string | null
          current_step?: number | null
          draft_id?: number
          expires_at?: string | null
          form_data?: Json | null
          furthest_step?: number | null
          last_updated?: string | null
          reviewer_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "draft_reviews_boss_id_fkey"
            columns: ["boss_id"]
            isOneToOne: false
            referencedRelation: "bosses"
            referencedColumns: ["boss_id"]
          },
          {
            foreignKeyName: "draft_reviews_reviewer_id_fkey"
            columns: ["reviewer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      linkedin_profiles: {
        Row: {
          boss_id: string | null
          certifications: Json | null
          city: string | null
          country: string | null
          created_at: string | null
          education: Json | null
          experiences: Json | null
          full_name: string | null
          headline: string | null
          languages: Json | null
          last_updated: string | null
          linkedin_url: string
          profile_id: string
          profile_pic_url: string | null
          summary: string | null
          volunteer_work: Json | null
        }
        Insert: {
          boss_id?: string | null
          certifications?: Json | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          education?: Json | null
          experiences?: Json | null
          full_name?: string | null
          headline?: string | null
          languages?: Json | null
          last_updated?: string | null
          linkedin_url: string
          profile_id?: string
          profile_pic_url?: string | null
          summary?: string | null
          volunteer_work?: Json | null
        }
        Update: {
          boss_id?: string | null
          certifications?: Json | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          education?: Json | null
          experiences?: Json | null
          full_name?: string | null
          headline?: string | null
          languages?: Json | null
          last_updated?: string | null
          linkedin_url?: string
          profile_id?: string
          profile_pic_url?: string | null
          summary?: string | null
          volunteer_work?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "linkedin_profiles_boss_id_fkey"
            columns: ["boss_id"]
            isOneToOne: false
            referencedRelation: "bosses"
            referencedColumns: ["boss_id"]
          },
        ]
      }
      questionnaire_responses: {
        Row: {
          created_at: string | null
          question_id: string | null
          response_id: number
          review_id: number | null
          score: number
        }
        Insert: {
          created_at?: string | null
          question_id?: string | null
          response_id?: number
          review_id?: number | null
          score: number
        }
        Update: {
          created_at?: string | null
          question_id?: string | null
          response_id?: number
          review_id?: number | null
          score?: number
        }
        Relationships: [
          {
            foreignKeyName: "questionnaire_responses_review_id_fkey"
            columns: ["review_id"]
            isOneToOne: false
            referencedRelation: "reviews"
            referencedColumns: ["review_id"]
          },
        ]
      }
      relationships: {
        Row: {
          boss_id: string | null
          created_at: string
          relationship_id: number
          relationship_type: string | null
          user_id: string | null
        }
        Insert: {
          boss_id?: string | null
          created_at?: string
          relationship_id?: number
          relationship_type?: string | null
          user_id?: string | null
        }
        Update: {
          boss_id?: string | null
          created_at?: string
          relationship_id?: number
          relationship_type?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "relationships_boss_id_fkey"
            columns: ["boss_id"]
            isOneToOne: false
            referencedRelation: "bosses"
            referencedColumns: ["boss_id"]
          },
          {
            foreignKeyName: "relationships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      review_steps: {
        Row: {
          completed_at: string | null
          is_final_step: boolean | null
          is_required: boolean | null
          review_id: number | null
          step_id: number
          step_name: string
          step_order: number
        }
        Insert: {
          completed_at?: string | null
          is_final_step?: boolean | null
          is_required?: boolean | null
          review_id?: number | null
          step_id?: number
          step_name: string
          step_order: number
        }
        Update: {
          completed_at?: string | null
          is_final_step?: boolean | null
          is_required?: boolean | null
          review_id?: number | null
          step_id?: number
          step_name?: string
          step_order?: number
        }
        Relationships: [
          {
            foreignKeyName: "review_steps_review_id_fkey"
            columns: ["review_id"]
            isOneToOne: false
            referencedRelation: "reviews"
            referencedColumns: ["review_id"]
          },
        ]
      }
      review_votes: {
        Row: {
          created_at: string
          id: number
          is_positive: boolean | null
          review_id: number
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: number
          is_positive?: boolean | null
          review_id: number
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: number
          is_positive?: boolean | null
          review_id?: number
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "review_votes_review_id_fkey"
            columns: ["review_id"]
            isOneToOne: false
            referencedRelation: "reviews"
            referencedColumns: ["review_id"]
          },
          {
            foreignKeyName: "review_votes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      reviews: {
        Row: {
          boss_id: string | null
          company_id: string | null
          created_at: string | null
          experience_id: number | null
          furthest_step: number | null
          is_draft: boolean | null
          nps_score: number | null
          questionnaire_completed: boolean | null
          relationship: string | null
          review_content: string | null
          review_date: string | null
          review_headline: string | null
          review_id: number
          review_uuid: string | null
          reviewer_id: string | null
          sentiment_id: number | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          boss_id?: string | null
          company_id?: string | null
          created_at?: string | null
          experience_id?: number | null
          furthest_step?: number | null
          is_draft?: boolean | null
          nps_score?: number | null
          questionnaire_completed?: boolean | null
          relationship?: string | null
          review_content?: string | null
          review_date?: string | null
          review_headline?: string | null
          review_id?: number
          review_uuid?: string | null
          reviewer_id?: string | null
          sentiment_id?: number | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          boss_id?: string | null
          company_id?: string | null
          created_at?: string | null
          experience_id?: number | null
          furthest_step?: number | null
          is_draft?: boolean | null
          nps_score?: number | null
          questionnaire_completed?: boolean | null
          relationship?: string | null
          review_content?: string | null
          review_date?: string | null
          review_headline?: string | null
          review_id?: number
          review_uuid?: string | null
          reviewer_id?: string | null
          sentiment_id?: number | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reviews_boss_id_fkey"
            columns: ["boss_id"]
            isOneToOne: false
            referencedRelation: "bosses"
            referencedColumns: ["boss_id"]
          },
          {
            foreignKeyName: "reviews_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["company_id"]
          },
          {
            foreignKeyName: "reviews_experience_id_fkey"
            columns: ["experience_id"]
            isOneToOne: false
            referencedRelation: "work_experiences"
            referencedColumns: ["experience_id"]
          },
          {
            foreignKeyName: "reviews_reviewer_id_fkey"
            columns: ["reviewer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_sentiment_id_fkey"
            columns: ["sentiment_id"]
            isOneToOne: false
            referencedRelation: "sentiments"
            referencedColumns: ["sentiment_id"]
          },
        ]
      }
      roles: {
        Row: {
          role_id: number
          role_name: string
        }
        Insert: {
          role_id?: number
          role_name: string
        }
        Update: {
          role_id?: number
          role_name?: string
        }
        Relationships: []
      }
      saved_bosses: {
        Row: {
          boss_id: string
          created_at: string | null
          id: string
          user_id: string
        }
        Insert: {
          boss_id: string
          created_at?: string | null
          id?: string
          user_id: string
        }
        Update: {
          boss_id?: string
          created_at?: string | null
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "saved_bosses_boss_id_fkey"
            columns: ["boss_id"]
            isOneToOne: false
            referencedRelation: "bosses"
            referencedColumns: ["boss_id"]
          },
          {
            foreignKeyName: "saved_bosses_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      sentiments: {
        Row: {
          boss_id: string
          created_at: string | null
          review_id: number | null
          reviewer_id: string | null
          sentiment: number
          sentiment_id: number
        }
        Insert: {
          boss_id: string
          created_at?: string | null
          review_id?: number | null
          reviewer_id?: string | null
          sentiment: number
          sentiment_id?: number
        }
        Update: {
          boss_id?: string
          created_at?: string | null
          review_id?: number | null
          reviewer_id?: string | null
          sentiment?: number
          sentiment_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_review"
            columns: ["review_id"]
            isOneToOne: false
            referencedRelation: "reviews"
            referencedColumns: ["review_id"]
          },
          {
            foreignKeyName: "sentiments_boss_id_fkey"
            columns: ["boss_id"]
            isOneToOne: false
            referencedRelation: "bosses"
            referencedColumns: ["boss_id"]
          },
          {
            foreignKeyName: "sentiments_reviewer_id_fkey"
            columns: ["reviewer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          role_id: number
          user_id: number
        }
        Insert: {
          role_id: number
          user_id: number
        }
        Update: {
          role_id?: number
          user_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["role_id"]
          },
        ]
      }
      users: {
        Row: {
          deleted_at: string | null
          email: string | null
          id: string
          role_id: number | null
        }
        Insert: {
          deleted_at?: string | null
          email?: string | null
          id: string
          role_id?: number | null
        }
        Update: {
          deleted_at?: string | null
          email?: string | null
          id?: string
          role_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "users_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["role_id"]
          },
        ]
      }
      work_experiences: {
        Row: {
          boss_id: string
          company_id: string | null
          end_date: string | null
          experience_id: number
          is_current: boolean | null
          is_custom: boolean
          is_verified: boolean | null
          job_title: string | null
          start_date: string | null
          user_id: string | null
        }
        Insert: {
          boss_id: string
          company_id?: string | null
          end_date?: string | null
          experience_id?: number
          is_current?: boolean | null
          is_custom?: boolean
          is_verified?: boolean | null
          job_title?: string | null
          start_date?: string | null
          user_id?: string | null
        }
        Update: {
          boss_id?: string
          company_id?: string | null
          end_date?: string | null
          experience_id?: number
          is_current?: boolean | null
          is_custom?: boolean
          is_verified?: boolean | null
          job_title?: string | null
          start_date?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "work_experiences_boss_id_fkey"
            columns: ["boss_id"]
            isOneToOne: false
            referencedRelation: "bosses"
            referencedColumns: ["boss_id"]
          },
          {
            foreignKeyName: "work_experiences_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["company_id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calculate_featured_communicators: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      calculate_featured_growth: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      calculate_featured_strategic_operators: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      calculate_featured_team_champ: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      calculate_featured_top_rated: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      calculate_featured_trending: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      calculate_featured_values: {
        Args: { boss_id: string }
        Returns: number
      }
      calculate_featured_wild_cards: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      reviews_count: {
        Args: { user_row: Database["public"]["Tables"]["users"]["Row"] }
        Returns: number
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
