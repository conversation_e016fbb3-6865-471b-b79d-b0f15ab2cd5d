"use client";

import { signInAction } from "@/app/actions";
import { Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { UserRoundCog, Linkedin,  } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {googleSignIn, linkedInSignIn} from "@/utils/auth";
import GoogleIcon from '@/assets/google-icon-logo.svg';
import { useSearchParams } from 'next/navigation'


export default function SignIn() {
  const searchParams = useSearchParams()
  const redirect = searchParams.get('redirect')

  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSignIn = async (formData: FormData) => {
    setError(null);
    try {

      if (redirect) {
        formData.append('redirect', redirect)
      }

      const result = await signInAction(formData);
      if (result?.error) {
        if (result.error.toLowerCase().includes("email not confirmed")) {
          setError(
            "Please check your email and confirm your account before signing in. If you need a new confirmation email, please sign up again.",
          );
        } else {
          setError(result.error);
        }
      }
    } catch (error) {
      console.log("===> Redirecting...");
    }
  };

  const handleLinkedInSignIn = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await linkedInSignIn(redirect);
    } catch (error) {
      console.error("Error during LinkedIn sign in:", error);
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await googleSignIn(redirect);
    } catch (error) {
      console.error("Error during Google sign in:", error);
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full flex items-center justify-center h-full">
      <div className="w-full max-w-md px-4 py-8">
        <Card>
          <CardHeader className="space-y-1 flex flex-col items-center">
            <div className="bg-primary/10 p-3 rounded-full mb-3">
              <UserRoundCog className="h-6 w-6 text-primary" />
            </div>
            <CardTitle >
              <h1 className="text-2xl font-bold text-center">Welcome Back</h1>
            </CardTitle>
            <p className="text-sm text-muted-foreground text-center">
              Don't have an account?{" "}
              <Link
                className="text-primary hover:text-primary/80 font-medium"
                href="/sign-up"
              >
                Sign up
              </Link>
            </p>
          </CardHeader>
          <CardContent>
            <div className={"flex flex-col gap-4"}>
              {/* LinkedIn Sign In Button */}
              <Button
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2"
                  onClick={handleLinkedInSignIn}
                  disabled={isLoading}
              >
                <Linkedin className="h-5 w-5 text-[#0A66C2]" />
                {isLoading ? "Connecting..." : "Sign in with LinkedIn"}
              </Button>
              {/* Google Sign In Button */}
              <Button
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2"
                  onClick={handleGoogleSignIn}
                  disabled={isLoading}
              >
                <GoogleIcon className="h-5 w-5" />
                {isLoading ? "Connecting..." : "Sign in with Google"}
              </Button>
            </div>

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center">
                <span className="bg-card px-2 text-xs text-muted-foreground">
                  OR CONTINUE WITH EMAIL
                </span>
              </div>
            </div>
            <form action={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    className="text-xs text-primary hover:text-primary/80"
                    href="/forgot-password"
                  >
                    Forgot Password?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  name="password"
                  placeholder="Enter your password"
                  required
                  className="w-full"
                />
              </div>

              <SubmitButton className="w-full" pendingText="Signing In...">
                Sign in
              </SubmitButton>

              {error && (
                <div className="p-3 rounded-md bg-destructive/10 text-destructive text-sm">
                  {error}
                </div>
              )}
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
