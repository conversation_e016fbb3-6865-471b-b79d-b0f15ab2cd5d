"use client";

import React, { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Linkedin, UserPlus } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { checkThatEmailAvailable } from "@/app/actions";
import {googleSignIn, linkedInSignIn} from "@/utils/auth";
import GoogleIcon from "@/assets/google-icon-logo.svg";

const SignUpPage: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    if (!isValidEmail(email)) {
      setEmailError("Please enter a valid email address.");
      setIsLoading(false);
      return;
    }

    const checkResult = await checkThatEmailAvailable(email);

    if (checkResult) {
      setError(checkResult.error);
    } else {
      const supabase = createClient();

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        if (error.message.includes("is invalid")) {
          setError("Your email is invalid")
        } else {
          setError(error.message);
        }
      } else {
        setIsSuccess(true);
      }
    }
    setIsLoading(false);
  };

  const handleLinkedInSignIn = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await linkedInSignIn()
    } catch (error) {
      console.error("Error during LinkedIn sign in:", error);
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await googleSignIn()
    } catch (error) {
      console.error("Error during LinkedIn sign in:", error);
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full flex items-center justify-center h-full">
      <div className="w-full max-w-md px-4 py-8">
        <Card>
          <CardHeader className="space-y-1 flex flex-col items-center">
            <div className="bg-primary/10 p-3 rounded-full mb-3">
              <UserPlus className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>
              <h1 className="text-2xl font-bold text-center">Create an Account</h1>
            </CardTitle>
            <p className="text-sm text-muted-foreground text-center">
              Already have an account?{" "}
              <Link
                className="text-primary hover:text-primary/80 font-medium"
                href="/sign-in"
              >
                Sign in
              </Link>
            </p>
          </CardHeader>
          <CardContent className="min-h-[300px]">
            {isSuccess ? (
              <div className="space-y-4">
                <div className="p-3 rounded-md bg-green-50 text-green-600 text-sm text-center">
                  Please check your email to confirm your account registration.
                </div>
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={() => (window.location.href = "/sign-in")}
                >
                  Return to Sign In
                </Button>
              </div>
            ) : (
              <>
                <div className={"flex flex-col gap-4"}>
                  {/* LinkedIn Sign In Button */}
                  <Button
                      variant="outline"
                      className="w-full flex items-center justify-center gap-2"
                      onClick={handleLinkedInSignIn}
                      disabled={isLoading}
                  >
                    <Linkedin className="h-5 w-5 text-[#0A66C2]" />
                    {isLoading ? "Connecting..." : "Sign in with LinkedIn"}
                  </Button>
                  {/* Google Sign In Button */}
                  <Button
                      variant="outline"
                      className="w-full flex items-center justify-center gap-2"
                      onClick={handleGoogleSignIn}
                      disabled={isLoading}
                  >
                    <GoogleIcon className="h-5 w-5" />
                    {isLoading ? "Connecting..." : "Sign in with Google"}
                  </Button>
                </div>

                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <Separator className="w-full" />
                  </div>
                  <div className="relative flex justify-center">
                    <span className="bg-card px-2 text-xs text-muted-foreground">
                      OR CONTINUE WITH EMAIL
                    </span>
                  </div>
                </div>
                <form onSubmit={handleSignUp} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => {
                        setEmail(e.target.value);
                        setEmailError(null);
                      }}
                      required
                      className="w-full"
                    />
                    {emailError && (
                      <p className="text-xs text-destructive">{emailError}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="password">Password</Label>
                    </div>
                    <Input
                      id="password"
                      type="password"
                      name="password"
                      placeholder="Create a password"
                      minLength={6}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="w-full"
                    />
                  </div>

                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Creating Account..." : "Create Account"}
                  </Button>

                  {error && (
                    <div className="p-3 rounded-md bg-destructive/10 text-destructive text-sm break-words whitespace-pre-wrap">
                      {error}
                    </div>
                  )}
                </form>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SignUpPage;
