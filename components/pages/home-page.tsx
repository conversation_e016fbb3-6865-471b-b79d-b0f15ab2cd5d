"use client";

import { useEffect, useRef, useState } from "react";
import { Session } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/client";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  AlertCircle,
  ArrowRight,
  BadgeCheck,
  ChevronLeft,
  ChevronRight,
  Megaphone,
  MessageSquare,
  Shield,
  Users2,
} from "lucide-react";
import { LinkedInCorrect } from "@/components/ui/linkedin-incorrect";
import { GlassdoorInspired } from "@/components/ui/glassdoor-inspired";
import {routes} from "@/utils/routes";
import {useUser} from "@/contexts/UserContext";

// Define search category types and their parameters
interface SearchCategoryConfig {
  label: string;
  query: (searchTerm: string) => {
    table: string;
    searchQuery: any; // Supabase query parameters
    joins?: string[];
    filters?: any;
  };
  placeholder: string;
}

const searchCategories: Record<string, SearchCategoryConfig> = {
  top_rated: {
    label: "Top Rated",
    query: (searchTerm) => ({
      table: "bosses",
      searchQuery: {
        select: `
          *,
          reviews!inner (
            review_id,
            status,
            sentiment_id
          ),
          companies (
            name
          )
        `,
        filters: {
          status: "published",
          average_rating: { gte: 4 },
        },
      },
      joins: ["reviews", "companies"],
    }),
    placeholder: "Search top rated leaders...",
  },
  most_reviewed: {
    label: "Most Reviewed",
    query: (searchTerm) => ({
      table: "bosses",
      searchQuery: {
        select: `
          *,
          reviews!inner (
            review_id,
            status
          ),
          companies (
            name
          )
        `,
        order: [{ column: "review_count", ascending: false }],
        filters: {
          status: "published",
        },
      },
      joins: ["reviews", "companies"],
    }),
    placeholder: "Search most reviewed leaders...",
  },
  trending: {
    label: "Trending",
    query: (searchTerm) => ({
      table: "bosses",
      searchQuery: {
        select: `
          *,
          reviews!inner (
            review_id,
            status,
            created_at
          ),
          companies (
            name
          )
        `,
        filters: {
          status: "published",
          created_at: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          },
        },
        order: [{ column: "review_count", ascending: false }],
      },
      joins: ["reviews", "companies"],
    }),
    placeholder: "Search trending leaders...",
  },
};

export default function HomePage() {
  const {refreshUser} = useUser()
  const [session, setSession] = useState<Session | null>(null);
  const [reviewCount, setReviewCount] = useState(0);
  const [bossCount, setBossCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("top_rated");
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const supabase = createClient();

  useEffect(() => {
    refreshUser()
    const fetchData = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setSession(session);

      // if (session) {
      //   window.location.href = '/profile';
      //   return;
      // }

      // Fetch stats
      const { count: reviewCount } = await supabase
        .from("reviews")
        .select("*", { count: "exact", head: true })
        .eq("status", "approved");

      const { count: bossCount } = await supabase
        .from("bosses")
        .select("*", { count: "exact", head: true })
        .is("deleted_at", null);

      setReviewCount(reviewCount ?? 0);
      setBossCount(bossCount ?? 0);
    };

    fetchData();
  }, []);

  const handleSearch = async () => {
    setIsLoading(true);
    try {
      const category = searchCategories[selectedCategory];
      const { table, searchQuery, joins = [] } = category.query(searchTerm);

      let query = supabase.from(table).select(searchQuery.select);

      // Apply filters
      if (searchQuery.filters) {
        Object.entries(searchQuery.filters).forEach(([key, value]) => {
          if (typeof value === "object") {
            Object.entries(value as any).forEach(([operator, operandValue]) => {
              query = query.filter(key, operator, operandValue);
            });
          } else {
            query = query.eq(key, value);
          }
        });
      }

      // Apply ordering
      if (searchQuery.order) {
        searchQuery.order.forEach((orderBy: any) => {
          query = query.order(orderBy.column, { ascending: orderBy.ascending });
        });
      }

      const { data, error } = await query;

      if (error) throw error;
      setSearchResults(data || []);
    } catch (error) {
      console.error("Search error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <HeroSection reviewCount={reviewCount} bossCount={bossCount} />
      <CalloutSection />
      <FeatureSection />
      <WhyRateMyBossSection />
      <CallToActionSection />
    </div>
  );
}

const HeroSection = ({
  reviewCount,
  bossCount,
}: {
  reviewCount: number;
  bossCount: number;
}) => (
  <section className="relative pt-16 pb-8 overflow-hidden min-h-[80vh] flex items-center isolate">
    {/* Background Elements */}
    <div className="absolute inset-0 -z-10">
      {/* Subtle radial gradient background */}
      <div
        className="absolute inset-0 bg-gradient-to-br from-[#7322A5]/5 via-transparent to-[#FF6B57]/5"
        style={{
          backgroundImage: `
            radial-gradient(
              circle at 50% 50%,
              rgba(115, 34, 165, 0.03),
              transparent 70%
            )
          `,
        }}
      />

      {/* Soft floating circles */}
      <div className="absolute top-1/4 right-1/4 w-[40rem] h-[40rem] bg-[#FF6B57]/5 rounded-full blur-[100px] animate-float" />
      <div className="absolute bottom-1/4 left-1/4 w-[40rem] h-[40rem] bg-[#7322A5]/5 rounded-full blur-[100px] animate-float-delayed" />
    </div>

    <div className="container mx-auto px-4 relative">
      <div className="max-w-4xl mx-auto text-center">
        {/* Main Heading - Simplified with gradient */}
        <h1 className="text-5xl sm:text-6xl md:text-7xl font-bold tracking-tight mb-6">
          Your Voice,
          <br />
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#7322A5] to-[#FF6B57]">
            Amplified.
          </span>
        </h1>

        {/* Subheading */}
        <p className="text-lg sm:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
          Get the real story about the bosses you work for—straight from the
          people who lived it. No corporate spin. No HR filters. Just the truth.
        </p>

        {/* CTA Buttons - Updated with sound wave hover effect */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            className="bg-[#7322A5] hover:bg-[#7322A5]/90 text-white relative overflow-hidden group"
            asChild
          >
            <Link href={routes.findBoss} className="flex items-center">
              Rate Your Boss
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </Button>
        </div>

        {/* Trust Indicators with updated icons */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-8 mt-16 text-center">
          <div className="flex flex-col items-center">
            <Megaphone className="h-5 w-5 text-primary mb-1" />
            <div className="text-2xl font-bold text-foreground mb-1">
              {reviewCount > 0 ? `${reviewCount.toLocaleString()}+` : "0"}
            </div>
            <div className="text-sm text-muted-foreground">
              Voices Amplified
            </div>
          </div>
          <div className="flex flex-col items-center">
            <Users2 className="h-5 w-5 text-primary mb-1" />
            <div className="text-2xl font-bold text-foreground mb-1">
              {bossCount > 0 ? `${bossCount.toLocaleString()}+` : "0"}
            </div>
            <div className="text-sm text-muted-foreground">Bosses Rated</div>
          </div>
          <div className="flex flex-col items-center">
            <Shield className="h-5 w-5 text-primary mb-1" />
            <div className="text-2xl font-bold text-foreground mb-1">100%</div>
            <div className="text-sm text-muted-foreground">Anonymous</div>
          </div>
          <div className="flex flex-col items-center">
            <BadgeCheck className="h-5 w-5 text-primary mb-1" />
            <div className="text-2xl font-bold text-foreground mb-1">24/7</div>
            <div className="text-sm text-muted-foreground">Unfiltered</div>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const CalloutSection = () => (
  <section className="relative pt-8 pb-12 overflow-hidden">
    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-secondary/10 to-transparent" />
    <div className="container mx-auto px-4 relative">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="font-semibold mb-6 text-foreground">
          <span className="block mb-2 text-3xl sm:text-4xl tracking-tight">
            Employees don't quit companies.
          </span>
          <span className="block text-4xl sm:text-5xl tracking-tight">
            They quit <span className="text-[#7322A5]">bosses</span>.
          </span>
        </h2>
        <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto">
          Your boss defines your work life more than your job description. Make
          sure you're not walking into a nightmare.
        </p>
      </div>
    </div>
  </section>
);

const FeatureSection = () => {
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const features = [
    {
      icon: <MessageSquare className="w-10 h-10 text-primary" />,
      title: "Real Reviews",
      description:
        "LinkedIn profiles are all shine, no substance. Get honest, firsthand feedback from employees who've worked with these bosses.",
    },
    {
      icon: <AlertCircle className="w-10 h-10 text-primary" />,
      title: "Straight Talk",
      description:
        "HR loves to sugarcoat things, but we're not here for that. Get straightforward insights on the people who will shape your career.",
    },
    {
      icon: <Shield className="w-10 h-10 text-primary" />,
      title: "Your Voice, Protected",
      description:
        "Share your experiences openly, knowing you're in a space designed for honesty, not retaliation.",
    },
  ];

  const updateScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  const scroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollAmount = container.clientWidth * 0.8;
      container.scrollBy({
        left: direction === "left" ? -scrollAmount : scrollAmount,
        behavior: "smooth",
      });
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      updateScrollButtons();
      container.addEventListener("scroll", updateScrollButtons);
      window.addEventListener("resize", updateScrollButtons);
    }

    return () => {
      if (container) {
        container.removeEventListener("scroll", updateScrollButtons);
        window.removeEventListener("resize", updateScrollButtons);
      }
    };
  }, []);

  const FeatureCard = ({
    icon,
    title,
    description,
  }: {
    icon: React.ReactNode;
    title: string;
    description: string;
  }) => (
    <div className="bg-background p-8 rounded-2xl shadow-sm h-full flex flex-col">
      <div className="flex items-start mb-6">
        <div className="mr-4">{icon}</div>
        <h3 className="text-xl font-semibold tracking-tight">{title}</h3>
      </div>
      <p className="text-muted-foreground leading-relaxed flex-grow">
        {description}
      </p>
    </div>
  );

  return (
    <section className="py-12 bg-background">
      <div className="container mx-auto px-4">
        {/* Desktop View */}
        <div className="hidden md:grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
            />
          ))}
        </div>

        {/* Mobile Carousel */}
        <div className="md:hidden relative">
          <div
            ref={scrollContainerRef}
            className="overflow-x-auto flex gap-4 pb-4 scrollbar-hide snap-x snap-mandatory"
            style={{
              scrollbarWidth: "none",
              msOverflowStyle: "none",
              maskImage:
                "linear-gradient(to right, transparent, black 2%, black 98%, transparent)",
              WebkitMaskImage:
                "linear-gradient(to right, transparent, black 2%, black 98%, transparent)",
            }}
          >
            {features.map((feature, index) => (
              <div
                key={index}
                className="flex-none w-[85vw] max-w-sm snap-center"
              >
                <FeatureCard
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                />
              </div>
            ))}
          </div>

          {/* Scroll Controls */}
          {canScrollLeft && (
            <div className="absolute left-0 top-1/2 -translate-y-1/2 z-10">
              <div className="absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-background to-transparent" />
              <button
                onClick={() => scroll("left")}
                className="relative z-20 h-8 w-8 flex items-center justify-center bg-background border rounded-full shadow-md hover:bg-muted transition-colors ml-2"
                aria-label="Scroll left"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
            </div>
          )}

          {canScrollRight && (
            <div className="absolute right-0 top-1/2 -translate-y-1/2 z-10">
              <div className="absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-background to-transparent" />
              <button
                onClick={() => scroll("right")}
                className="relative z-20 h-8 w-8 flex items-center justify-center bg-background border rounded-full shadow-md hover:bg-muted transition-colors mr-2"
                aria-label="Scroll right"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

const WhyRateMyBossSection = () => (
  <section className="relative py-12 overflow-hidden">
    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-secondary/10 to-transparent" />
    <div className="container mx-auto px-4 relative">
      <h2 className="text-3xl sm:text-4xl font-semibold mb-8 text-center text-foreground tracking-tight">
        What They Don't Want You to Know.
      </h2>

      <div className="grid md:grid-cols-2 gap-6 max-w-5xl mx-auto">
        <div className="bg-background p-8 rounded-2xl border transition-all hover:shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold tracking-tight">
              The LinkedIn Lie
            </h3>
            <LinkedInCorrect />
          </div>
          <p className="text-muted-foreground mb-4 leading-relaxed">
            Leaders hide behind carefully crafted profiles, hollow buzzwords,
            and inflated endorsements.
          </p>
          <p className="text-muted-foreground leading-relaxed">
            You get a shiny, sugarcoated version of someone who has a huge
            impact on your work life, but no real insight.
          </p>
        </div>

        <div className="bg-background p-8 rounded-2xl border transition-all hover:shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold tracking-tight">
              The Glassdoor Gimmick
            </h3>
            <GlassdoorInspired />
          </div>
          <p className="text-muted-foreground mb-4 leading-relaxed">
            Glassdoor might look transparent, but it's really a one-way mirror
            for HR to keep tabs on feedback.
          </p>
          <p className="text-muted-foreground leading-relaxed">
            You get a glimpse of the workplace, but not the reality of who
            you'll actually be working for.
          </p>
        </div>
      </div>

      <div className="bg-primary/5 p-8 rounded-2xl mt-6 max-w-5xl mx-auto border-t border-primary/10">
        <h3 className="text-xl font-semibold mb-4 tracking-tight">
          The Amplify Difference
        </h3>
        <p className="text-muted-foreground mb-4 leading-relaxed">
          Your voice matters. Whether you want to expose a toxic culture or
          celebrate a great leader, we're here to make sure employees—not
          employers—shape the narrative.
        </p>
        <p className="text-lg font-medium tracking-tight">
          It's time to cut through the LinkedIn lies and get the real story
          behind the people who shape your career.
        </p>
      </div>
    </div>
  </section>
);

const CallToActionSection = () => (
  <section className="relative py-12 bg-secondary/10">
    <div className="container mx-auto px-4 relative">
      {/* Subtle decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -right-1/4 top-1/2 w-1/2 h-1/2 bg-[#7322A5]/5 rounded-full blur-[100px]" />
        <div className="absolute -left-1/4 top-0 w-1/2 h-1/2 bg-[#FF6B57]/5 rounded-full blur-[100px]" />
      </div>

      {/* Content with refined styling */}
      <div className="relative max-w-4xl mx-auto text-center">
        <h2 className="text-2xl sm:text-3xl font-semibold mb-4 tracking-tight">
          Your Story. Your Experience.{" "}
          <span className="text-[#7322A5]">Your Voice, Amplified.</span>
        </h2>
        <p className="text-lg sm:text-xl mb-8 max-w-2xl mx-auto text-muted-foreground">
          What you share isn't just a review—it's a warning, a wake-up call, and
          a reality check for the next person considering that job.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button
            size="lg"
            className="bg-[#7322A5] hover:bg-[#7322A5]/90 text-white hover:-translate-y-1 transition-all"
            asChild
          >
            <Link href={routes.findBoss}>
              Amplify Your Voice
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  </section>
);
