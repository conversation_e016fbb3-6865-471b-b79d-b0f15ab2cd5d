"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON>ertCircle, Loader2 } from "lucide-react";
import { getLinkedInProfile } from "@/app/linkedin-actions";
import { toast } from "sonner";
import { useAuth } from "@/hooks/useAuth";
import SignInRequiredDialog from "@/components/dialogs/sign-in-required-dialog";
import { routes } from "@/utils/routes";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ValidationOptions, validateInput } from "@/utils/input-validator";
import { cn } from "@/utils/cn";

const createWorkExperiencesFromLinkedIn = async (
  supabase: any,
  boss_id: number,
  experiences: any[],
) => {
  try {
    // Process each experience from LinkedIn
    for (const exp of experiences) {
      // First, check/create company for each experience
      let company_id: number;
      const { data: existingCompany, error: companyError } = await supabase
        .from("companies")
        .select("company_id")
        .ilike("name", exp.company)
        .single();

      if (companyError) {
        // Company doesn't exist, create it
        const { data: newCompany, error: createCompanyError } = await supabase
          .from("companies")
          .insert({
            name: exp.company,
          })
          .select("company_id")
          .single();

        if (createCompanyError) throw createCompanyError;
        company_id = newCompany.company_id;
      } else {
        company_id = existingCompany.company_id;
      }

      const { error: workExpError } = await supabase
        .from("work_experiences")
        .insert({
          boss_id: boss_id,
          company_id: company_id,
          job_title: exp.title,
          start_date: exp.starts_at
            ? `${exp.starts_at.year}-${exp.starts_at.month}-01`
            : null,
          end_date: exp.ends_at
            ? `${exp.ends_at.year}-${exp.ends_at.month}-01`
            : null,
          is_current: !exp.ends_at,
        });

      if (workExpError) {
        console.error("Error creating work experience:", workExpError);
        throw workExpError;
      }
    }
  } catch (error) {
    console.error("Error creating work experiences:", error);
    throw new Error("Failed to create work experiences");
  }
};

const TEXT_VALIDATION_OPTIONS: ValidationOptions = {
  minLength: 1,
  maxLength: 50,
  allowHtml: false,
  allowUrls: false,
  allowSpecialChars: true,
  required: false,
};

const AddBossPage: React.FC = () => {
  const [validationError, setValidationError] = useState<string>("");
  // Shared state
  const [name, setName] = useState("");
  const [company, setCompany] = useState("");
  const [title, setTitle] = useState("");
  const [linkedinUrl, setLinkedinUrl] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isLinkedInVerified, setIsLinkedInVerified] = useState(false);
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const [activeTab, setActiveTab] = useState("linkedin");

  // Manual entry form state
  const [manualName, setManualName] = useState("");
  const [manualCompany, setManualCompany] = useState("");
  const [manualTitle, setManualTitle] = useState("");
  const [manualFormValid, setManualFormValid] = useState(false);

  const router = useRouter();
  const pathname = usePathname();
  const { user, loading } = useAuth();

  // Check if user is authenticated when the component mounts
  useEffect(() => {
    if (!loading && !user) {
      setShowAuthDialog(true);
    }
  }, [user, loading]);

  // Validate manual form fields
  useEffect(() => {
    const isValid =
      manualName.trim().length > 0 &&
      manualCompany.trim().length > 0 &&
      manualTitle.trim().length > 0;

    setManualFormValid(isValid);
  }, [manualName, manualCompany, manualTitle]);

  // Handle login action when user clicks Sign In button in dialog
  const handleLogin = () => {
    router.push(`/sign-in?redirect=${encodeURIComponent(pathname)}`);
  };

  const verifyLinkedInProfile = async () => {
    // Check if user is authenticated before proceeding
    if (!user) {
      setShowAuthDialog(true);
      return;
    }

    setIsVerifying(true);
    setError("");

    try {
      // Use the server function directly
      const result = await getLinkedInProfile(linkedinUrl);

      if (result.error) {
        throw new Error(result.error);
      }

      const data = result.data;
      if (!data) {
        throw new Error("No data returned from LinkedIn profile");
      }

      // Store the complete LinkedIn profile data
      const supabase = createClient();
      const { error: profileError } = await supabase
        .from("linkedin_profiles")
        .insert({
          linkedin_url: linkedinUrl,
          full_name: data.full_name,
          headline: data.headline,
          summary: data.summary,
          country: data.country,
          city: data.city,
          profile_pic_url: data.profile_pic_url,
          experiences: data.experiences,
        });

      if (profileError) {
        console.error("Error storing LinkedIn profile:", profileError);
      }

      // Extract the current experience (first in the array)
      const currentExperience = data.experiences?.[0];
      const companyName = currentExperience?.company || "";
      const jobTitle = currentExperience?.title || "";

      // Auto-fill form fields with LinkedIn data
      setName(data.full_name || "");
      setCompany(companyName);
      setTitle(jobTitle);
      setIsLinkedInVerified(true);

      toast.success("LinkedIn profile verified successfully!");
    } catch (error) {
      console.error("Error verifying LinkedIn profile:", error);
      // Get the specific error message from the error object if available
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to verify LinkedIn profile. Please try again or enter details manually.";

      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsVerifying(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user is authenticated before proceeding
    if (!user) {
      setShowAuthDialog(true);
      return;
    }

    // FIELDS VALIDATION PROCESS
    if (activeTab === "manual") {
      const fields = [
        { value: manualName, name: "Name" },
        { value: manualCompany, name: "Company" },
        { value: manualTitle, name: "Title" },
      ];

      for (const { value, name } of fields) {
        const result = validateInput(value, TEXT_VALIDATION_OPTIONS);
        if (!result.valid) {
          setValidationError(result.message || `Invalid ${name}`);
          return false;
        }
      }
    }

    setError("");
    setIsLoading(true);

    const supabase = createClient();

    try {
      if (activeTab === "manual") {
        const bossName = manualName;
        const companyName = manualCompany;
        const jobTitle = manualTitle;

        // First, check if the company exists, if not create it
        let { data: companyData, error: companyError } = await supabase
          .from("companies")
          .select("company_id")
          .eq("name", companyName)
          .single();

        if (companyError) {
          if (companyError.code === "PGRST116") {
            // Company doesn't exist, so create it
            const { data: newCompany, error: newCompanyError } = await supabase
              .from("companies")
              .insert({
                name: companyName,
              })
              .select("company_id")
              .single();

            if (newCompanyError) throw newCompanyError;
            companyData = newCompany;
          } else {
            throw companyError;
          }
        }

        // Check if the boss exists, if not create it
        let { data: bossData, error: bossError } = await supabase
          .from("bosses")
          .select("boss_id")
          .eq("name", bossName)
          .single();

        if (bossError) {
          if (bossError.code === "PGRST116") {
            // Boss doesn't exist, so create it
            const { data: newBoss, error: newBossError } = await supabase
              .from("bosses")
              .insert({
                name: bossName,
              })
              .select("boss_id")
              .single();

            if (newBossError) throw newBossError;
            bossData = newBoss;
          } else {
            throw bossError;
          }
        }
        const { error: workExperienceError } = await supabase
          .from("work_experiences")
          .insert({
            boss_id: bossData?.boss_id,
            company_id: companyData?.company_id,
            job_title: jobTitle,
          });

        if (workExperienceError) throw workExperienceError;

        toast.success("Boss added successfully!");
        router.push(routes.boss(bossData?.boss_id));
      } else {
        // 1. Get LinkedIn profile data from our linkedin_profiles table
        const { data: linkedinProfile, error: linkedinError } = await supabase
          .from("linkedin_profiles")
          .select("*")
          .eq("linkedin_url", linkedinUrl)
          .single();

        if (linkedinError) {
          console.error("Error fetching LinkedIn profile:", linkedinError);
        }

        // 2. Create or get the boss
        let boss_id: number;
        const { data: existingBoss, error: bossError } = await supabase
          .from("bosses")
          .select("boss_id")
          .eq("linkedin_url", linkedinProfile.linkedin_url)
          .single();

        if (bossError) {
          // Boss doesn't exist, create them
          const { data: newBoss, error: createBossError } = await supabase
            .from("bosses")
            .insert({
              name: linkedinProfile.full_name,
              linkedin_url: linkedinProfile.linkedin_url,
            })
            .select("boss_id")
            .single();

          if (createBossError) {
            throw new Error(
              "Failed to create boss: " + createBossError.message,
            );
          }
          boss_id = newBoss.boss_id;
        } else {
          boss_id = existingBoss.boss_id;
        }

        if (linkedinProfile.experiences) {
          // Create work experiences from LinkedIn data
          await createWorkExperiencesFromLinkedIn(
            supabase,
            boss_id,
            linkedinProfile.experiences,
          );
        }

        toast.success("Boss profile created successfully!");
        router.push(`/boss/${boss_id}`);
      }
    } catch (error) {
      console.error("Error adding boss:", error);
      const errorMessage = "Failed to add boss. Please try again.";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 min-h-[calc(100vh-4rem)] flex items-start justify-center">
      <div className="w-full max-w-lg bg-background rounded-lg border p-8 mt-8">
        <h1 className="text-2xl font-bold mb-4">Add a New Boss</h1>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="linkedin">Via LinkedIn</TabsTrigger>
            <TabsTrigger value="manual">Manual</TabsTrigger>
          </TabsList>

          <TabsContent value="linkedin">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label
                  htmlFor="linkedinUrl"
                  className="block text-sm font-medium text-gray-700"
                >
                  LinkedIn Profile URL
                </label>
                <div className="flex gap-2">
                  <Input
                    id="linkedinUrl"
                    type="url"
                    value={linkedinUrl}
                    onChange={(e) => {
                      setLinkedinUrl(e.target.value);
                      setIsLinkedInVerified(false);
                      // Clear all other form fields when LinkedIn URL changes
                      setName("");
                      setCompany("");
                      setTitle("");
                    }}
                    placeholder="https://www.linkedin.com/in/username"
                    className="flex-1 text-base"
                  />
                  <Button
                    type="button"
                    onClick={verifyLinkedInProfile}
                    disabled={
                      !linkedinUrl ||
                      isVerifying ||
                      isLoading ||
                      isLinkedInVerified
                    }
                    variant="secondary"
                    className={"text-center min-w-20"}
                  >
                    {isVerifying ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </>
                    ) : isLinkedInVerified ? (
                      "Verified"
                    ) : (
                      "Verify"
                    )}
                  </Button>
                </div>
              </div>

              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700"
                >
                  Boss Name
                </label>
                <div className="relative">
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                    disabled
                    className={cn(
                      "text-base",
                      !isLinkedInVerified
                        ? "opacity-70 cursor-not-allowed pr-10"
                        : "",
                    )}
                  />
                </div>
              </div>
              <div>
                <label
                  htmlFor="company"
                  className="block text-sm font-medium text-gray-700"
                >
                  Company
                </label>
                <div className="relative">
                  <Input
                    id="company"
                    value={company}
                    onChange={(e) => setCompany(e.target.value)}
                    required
                    disabled
                    className={cn(
                      "text-base",
                      !isLinkedInVerified
                        ? "opacity-70 cursor-not-allowed pr-10"
                        : "",
                    )}
                  />
                </div>
              </div>
              <div>
                <label
                  htmlFor="title"
                  className="block text-sm font-medium text-gray-700"
                >
                  Title
                </label>
                <div className="relative">
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    required
                    disabled
                    className={cn(
                      "text-base",
                      !isLinkedInVerified
                        ? "opacity-70 cursor-not-allowed pr-10"
                        : "",
                    )}
                  />
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
                  {error}
                </div>
              )}

              <Button
                type="submit"
                disabled={isLoading || isVerifying || !isLinkedInVerified}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding Boss...
                  </>
                ) : (
                  "Add Boss"
                )}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="manual">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label
                  htmlFor="manualName"
                  className="block text-sm font-medium text-gray-700"
                >
                  Boss Name
                </label>
                <Input
                  id="manualName"
                  value={manualName}
                  onChange={(e) => setManualName(e.target.value)}
                  placeholder="Enter boss name"
                  required
                  className={"text-base"}
                />
              </div>

              <div>
                <label
                  htmlFor="manualCompany"
                  className="block text-sm font-medium text-gray-700"
                >
                  Company
                </label>
                <Input
                  id="manualCompany"
                  value={manualCompany}
                  onChange={(e) => setManualCompany(e.target.value)}
                  placeholder="Enter company name"
                  required
                  className={"text-base"}
                />
              </div>

              <div>
                <label
                  htmlFor="manualTitle"
                  className="block text-sm font-medium text-gray-700"
                >
                  Job Title
                </label>
                <Input
                  id="manualTitle"
                  value={manualTitle}
                  onChange={(e) => setManualTitle(e.target.value)}
                  placeholder="Enter job title"
                  required
                  className={"text-base"}
                />
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
                  {error}
                </div>
              )}

              {validationError && (
                <div className="flex items-start gap-2 p-3 mb-2 bg-red-50 text-red-800 rounded-md">
                  <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                  <p className="text-sm">{validationError}</p>
                </div>
              )}

              <Button type="submit" disabled={isLoading || !manualFormValid}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding Boss...
                  </>
                ) : (
                  "Add Boss"
                )}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </div>

      <SignInRequiredDialog
        showDialog={showAuthDialog}
        setShowDialog={setShowAuthDialog}
        actionClick={handleLogin}
        description="Please sign in to add a new boss."
      />
    </div>
  );
};

export default AddBossPage;
