"use client";

import React, { useEffect } from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  MessageCircle,
  Scale,
  Shield,
  BadgeCheck,
} from "lucide-react";

const DonatePage = () => {

  // Function to open the Buy Me a Coffee widget
  const openBuyMeCoffeeWidget = () => {
    // Find the button element created by the Buy Me a Coffee widget
    const bmcButton = document.getElementById('bmc-wbtn');

    if (bmcButton) {
      // Add a small delay to ensure the widget is fully initialized
      setTimeout(() => {
        // Programmatically click the button
        bmcButton.click();
      }, 100);
    } else {
      console.warn("Buy Me a Coffee widget button not found. The widget may not be fully loaded yet.");

      // Try to load the script again if the button is not found
      const script = document.createElement('script');
      script.src = "https://cdnjs.buymeacoffee.com/1.0.0/widget.prod.min.js";
      script.setAttribute('data-name', 'BMC-Widget');
      script.setAttribute('data-id', 'amplify');
      script.setAttribute('data-description', 'Support me on Buy me a coffee!');
      script.setAttribute('data-message', 'Help keep Amplify plugged in. We run on tips, not corporate spin.');
      script.setAttribute('data-color', '#BD5FFF');
      script.setAttribute('data-position', 'Right');
      script.setAttribute('data-x_margin', '18');
      script.setAttribute('data-y_margin', '18');

      // Remove any existing script to avoid duplicates
      const existingScript = document.getElementById('bmc-widget-script');
      if (existingScript) {
        existingScript.remove();
      }

      script.id = 'bmc-widget-script';
      document.body.appendChild(script);

      // Try clicking the button after a delay
      setTimeout(() => {
        const retryButton = document.getElementById('bmc-wbtn');
        if (retryButton) {
          retryButton.click();
        }
      }, 1000);
    }
  };

  // Handle script loading and check for the button element
  useEffect(() => {
    // Define a function to check if the button element is available
    const checkButtonAvailable = () => {
      const bmcButton = document.getElementById('bmc-wbtn');
      return !!bmcButton;
    };

    // Check if the button is available every 500ms for up to 10 seconds
    let attempts = 0;
    const maxAttempts = 20; // 10 seconds total

    const interval = setInterval(() => {
      if (checkButtonAvailable() || attempts >= maxAttempts) {
        clearInterval(interval);
        if (attempts >= maxAttempts && !checkButtonAvailable()) {
          console.warn("Buy Me a Coffee widget button failed to appear after 10 seconds");
        } else if (checkButtonAvailable()) {
          console.log("Buy Me a Coffee widget button is now available");
        }
      }
      attempts++;
    }, 500);

    // Clean up the interval on component unmount
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <div className="flex justify-center items-center gap-3 mb-6">
            <MessageCircle className="h-8 w-8 text-primary" />
            <h1 className="text-4xl font-bold">Help Us Keep It Real</h1>
            <Scale className="h-8 w-8 text-primary" />
          </div>
        </motion.div>

        <div className="flex gap-8 mb-12">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Shield className="h-6 w-6 text-primary" />
                  <h2 className="text-xl font-semibold">Our Promise</h2>
                </div>
                <p className="text-muted-foreground mb-6">
                  RateMyBoss runs on truth, not ads or corporate backing. We're
                  here to give employees a place to speak honestly, but keeping
                  the lights on without big money behind us isn't easy.
                </p>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <BadgeCheck className="h-5 w-5 text-primary" />
                    <span>No corporate influence</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Scale className="h-5 w-5 text-primary" />
                    <span>Complete transparency</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <MessageCircle className="h-5 w-5 text-primary" />
                    <span>Community powered</span>
                  </div>
                  <div className={'flex justify-center'}>
                    <Button
                        className="w-full md:w-auto mt-4"
                        variant="default"
                        onClick={openBuyMeCoffeeWidget}
                    >
                      Support
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="text-center"
        >
          <Card className="bg-primary/5">
            <CardContent className="p-6">
              <div className="flex justify-center items-center gap-2 mb-4">
                <Shield className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">
                  Every Contribution Counts!
                </h3>
              </div>
              <p className="text-muted-foreground">
                Your support helps us maintain independence and keep the
                platform free from corporate influence. Together, we're building
                a more transparent workplace, one review at a time.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default DonatePage;
