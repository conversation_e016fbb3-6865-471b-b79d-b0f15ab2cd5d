"use client";

import React, { FC, useState } from "react";
import {
  ArrowD<PERSON>,
  ArrowRight,
  ArrowUp,
  Briefcase,
  Building2,
  Calendar,
  Star,
  User,
  UserCircle2,
  UserCog,
  Users,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { format } from "date-fns";
import type { QuestionnaireResponses } from "@/types/boss";
import {
  competencyCategories,
  questionnaireToCompetencyMap,
} from "@/app/boss/[id]/constants/dimensions";
import { cn } from "@/lib/utils";
import { useUser } from "@/contexts/UserContext";
import { clsx } from "clsx";
import {
  FacebookIcon,
  FacebookShareButton,
  LinkedinIcon,
  LinkedinShareButton,
  XIcon,
  TwitterShareButton,
} from "react-share";
import SignInRequiredDialog from "@/components/dialogs/sign-in-required-dialog";
import { usePathname, useRouter } from "next/navigation";
import { likeReviewAction } from "@/app/review-votes-actions";
import { toast } from "sonner";

const formatQuestionnaireResponses = (
  questionnaireResponses: QuestionnaireResponses[],
) => {
  return questionnaireResponses.reduce(
    (acc, response) => {
      const competency =
        questionnaireToCompetencyMap[
          response.question_id as keyof typeof questionnaireToCompetencyMap
        ];
      if (competency) {
        acc[competency] = response.score;
      }
      return acc;
    },
    {} as Record<string, number>,
  );
};

type ReviewPageProps = {
  review: {
    review_id: number;
    experience_id: number;
    review_content: string;
    relationship: string;
    review_date: string; // ISO date
    created_at: string; // ISO datetime
    boss_id: string;
    company_id: string;
    nps_score: number;
    questionnaire_completed: boolean;
    sentiment_id: string | null;
    reviewer_id: string;
    status: string;
    updated_at: string | null;
    review_headline: string;
    furthest_step: number;
    is_draft: boolean;
    review_uuid: string;
    bosses: {
      name: string;
      boss_id: string;
      user_id: string | null;
      is_claimed: boolean;
      linkedin_url: string;
    };
    companies: {
      name: string;
      founded: string | null;
      industry: string | null;
      location: string | null;
      is_custom: boolean;
      company_id: string;
      linkedin_url: string | null;
    };
    users: {
      id: string;
      email: string;
      role_id: string | null;
      deleted_at: string | null;
    };
    work_experiences: any;
  };
  questionnaireResponses: QuestionnaireResponses[];
  voteBalance: number;
};

type StarRatingProps = {
  npsScore: number;
};
const StarRating: FC<StarRatingProps> = ({ npsScore }) => {
  return (
    <div className="flex items-center gap-0.5">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={cn(
            "h-8 w-8",
            star <= npsScore
              ? "fill-primary text-primary"
              : "fill-muted text-muted",
          )}
        />
      ))}
    </div>
  );
};

// New StarRating component for competency scores
type CompetencyStarRatingProps = {
  score: number;
};
const CompetencyStarRating: FC<CompetencyStarRatingProps> = ({ score }) => {
  return (
    <div className="flex items-center gap-0.5">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={cn(
            "h-3 w-3",
            star <= score
              ? "fill-primary text-primary"
              : "fill-muted text-muted",
          )}
        />
      ))}
    </div>
  );
};

// Function to get relationship icon
const getRelationshipIcon = (relationship: string) => {
  const iconProps = {
    className: "h-4 w-4 text-primary",
  };

  switch (relationship?.toLowerCase()) {
    case "direct report":
      return <UserCircle2 {...iconProps} />;
    case "co-worker":
      return <Users {...iconProps} />;
    case "direct manager/supervisor":
      return <UserCog {...iconProps} />;
    default:
      return <User {...iconProps} />;
  }
};

const ReviewPage: FC<ReviewPageProps> = ({
  review,
  questionnaireResponses,
  voteBalance: reviewVoteBalance,
}) => {
  const shareUrl = `https://www.boss.reviews/review/${review.review_uuid}`;
  const shareTitle = "Review";

  const { user, votes } = useUser();
  const router = useRouter();
  const pathname = usePathname();

  const [isVoting, setIsVoting] = useState<boolean>(false);
  const [showAuthDialog, setShowAuthDialog] = useState<boolean>(false);
  const voteRecord = votes.find((vote) => vote.review_id === review.review_id);
  const [voteBalance, setVoteBalance] = useState<number>(reviewVoteBalance);
  const [userVote, setUserVote] = useState<boolean | null>(
    voteRecord?.is_positive ?? null,
  );

  const updateVote = async (voteType: boolean) => {
    if (!user) {
      setShowAuthDialog(true);
      return;
    }

    try {
      setIsVoting(true);
      await likeReviewAction(review.review_id, voteType);

      if (voteType) {
        if (userVote === true) {
          setUserVote(null);
          setVoteBalance((prev) => prev - 1);
          toast.info("Removed your upvote");
        } else if (userVote === null) {
          setUserVote(true);
          setVoteBalance((prev) => prev + 1);
          toast.success("Upvoted!");
        } else if (userVote === false) {
          setUserVote(true);
          setVoteBalance((prev) => prev + 2);
          toast.success("Changed to upvote!");
        }
      } else {
        if (userVote === true) {
          setUserVote(false);
          setVoteBalance((prev) => prev - 2);
          toast.success("Changed to downvote!");
        } else if (userVote === null) {
          setUserVote(false);
          setVoteBalance((prev) => prev - 1);
          toast.success("Downvoted!");
        } else if (userVote === false) {
          setUserVote(null);
          setVoteBalance((prev) => prev + 1);
          toast.info("Removed your downvote");
        }
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to update your vote. Please try again.");
    } finally {
      setIsVoting(false);
    }
  };

  const handleLogin = () => {
    router.push(`/sign-in?redirect=${encodeURIComponent(pathname)}`);
  };

  const formattedQuestionnaireResponses = formatQuestionnaireResponses(
    questionnaireResponses,
  );

  // Process strengths and growth opportunities
  const strengths = Object.entries(competencyCategories).map(
    ([name]) => {
      const score = formattedQuestionnaireResponses[name] || 0;
      return {
        name,
        score,
        relationships: [
          {
            relationship: review.relationship,
            score: score,
          },
        ],
      };
    },
  );

  return (
    <main className="px-6 py-16 max-w-5xl mx-auto space-y-12">
      <header className="space-y-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <h1 className="text-xl md:text-4xl lg:text-5xl font-bold text-foreground pr-2 overflow-hidden overflow-ellipsis whitespace-nowrap">
            {review.bosses.name} Review
          </h1>
          {/* Star rating positioned to the right of title per user preference */}
          <div className="flex items-center gap-2 self-start md:self-center">
            <StarRating npsScore={review.nps_score ?? 0} />
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 flex-shrink-0" />
            <span className="text-sm md:text-base">
              {format(new Date(review.review_date), "d MMMM yyyy")}
            </span>
          </div>
          {/* Relationship type display */}
          <div className="flex items-center gap-2">
            {getRelationshipIcon(review.relationship)}
            <span className="text-sm md:text-base font-medium text-primary">
              {review.relationship}
            </span>
          </div>
        </div>
        <div>
          <Separator className="my-6" />

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-4">
            {/* Boss Name */}
            <div className="text-center">
              <div className="group relative">
                <div className="flex flex-row gap-2 justify-center items-center text-lg font-semibold">
                  <span
                    className="truncate max-w-[250px] sm:max-w-[120px] md:max-w-[150px] lg:max-w-[180px] xl:max-w-[220px]"
                    title={review.bosses.name} // Native HTML tooltip as fallback
                  >
                    {review.bosses.name}
                  </span>
                </div>
                {/* Enhanced tooltip that appears on hover */}
                <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 bg-popover text-popover-foreground text-sm rounded shadow-lg border pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity z-50 w-auto max-w-[250px] whitespace-normal break-words">
                  {review.bosses.name}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Boss Name
                </div>
              </div>
            </div>

            {/* Company */}
            <div className="text-center">
              <div className="group relative">
                <div className="flex flex-row gap-2 justify-center items-center text-lg font-semibold">
                  <Building2 className="h-4 w-4 flex-shrink-0" />
                  <span
                    className="truncate max-w-[250px] sm:max-w-[120px] md:max-w-[150px] lg:max-w-[180px] xl:max-w-[220px]"
                    title={review.companies.name} // Native HTML tooltip as fallback
                  >
                    {review.companies.name}
                  </span>
                </div>
                {/* Enhanced tooltip that appears on hover */}
                <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 bg-popover text-popover-foreground text-sm rounded shadow-lg border pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity z-50 w-auto max-w-[250px] whitespace-normal break-words">
                  {review.companies.name}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Company
                </div>
              </div>
            </div>

            {/* Role */}
            <div className="text-center">
              <div className="group relative">
                <div className="flex flex-row gap-2 justify-center items-center text-lg font-semibold">
                  <Briefcase className="h-4 w-4 flex-shrink-0" />
                  <span
                    className="truncate max-w-[250px] sm:max-w-[120px] md:max-w-[150px] lg:max-w-[180px] xl:max-w-[220px]"
                    title={review.work_experiences.job_title} // Native HTML tooltip as fallback
                  >
                    {review.work_experiences.job_title}
                  </span>
                </div>
                {/* Enhanced tooltip that appears on hover */}
                <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 bg-popover text-popover-foreground text-sm rounded shadow-lg border pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity z-50 w-auto max-w-[250px] whitespace-normal break-words">
                  {review.work_experiences.job_title}
                </div>
                <div className="text-xs text-muted-foreground mt-1">Role</div>
              </div>
            </div>

            {/* Go to boss button */}
            <div className="text-center flex justify-center items-center">
              <Button
                className="bg-[#7322A5] hover:bg-[#7322A5]/90 text-white relative overflow-hidden group w-full sm:w-auto"
                asChild
              >
                <Link
                  href={`/boss/${review.bosses.boss_id}`}
                  className="flex items-center justify-center"
                >
                  Go to boss
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
            </div>
          </div>
          <Separator className="mt-6" />
        </div>
      </header>

      {/* Review headline moved directly above the quote */}
      <section className="space-y-4">
        <h2 className="text-2xl md:text-3xl font-semibold text-foreground">
          {review.review_headline}
        </h2>
        <div className="text-xl text-foreground break-words leading-relaxed italic border-l-4 border-[#7322A5] pl-6">
          "{review.review_content}"
        </div>
      </section>

      <section>
        <h2 className="text-xl font-semibold mb-6">The Review Breakdown</h2>

        {/* Leadership Strengths */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
            {strengths.map((competency) => {
              const Icon =
                competencyCategories[
                  competency.name as keyof typeof competencyCategories
                ]?.icon;

              return (
                <div
                  key={competency.name}
                  className="w-full py-0 md:py-2 px-0 md:px-4"
                >
                  {/* Header with Rating */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {Icon && (
                        <div className="p-1 rounded-full bg-primary/10">
                          <Icon className="h-4 w-4 text-primary" />
                        </div>
                      )}
                      <span className="text-sm font-medium">
                        {competency.name}
                      </span>
                    </div>
                    <CompetencyStarRating score={competency.score} />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>
      <div className="flex items-center justify-between gap-4 mt-4 pt-4 border-t">
        <div className={"flex items-center gap-1"}>
          <div className="flex items-center gap-1">
            <Button
              disabled={isVoting}
              onClick={() => updateVote(true)}
              variant="ghost"
              size="sm"
              aria-label="Upvote"
              title="Upvote"
              className={clsx(
                "h-6 w-6 p-0",
                userVote === true
                  ? "text-primary"
                  : "text-muted-foreground hover:text-primary",
              )}
            >
              <ArrowUp className="h-3.5 w-3.5" />
            </Button>
            <span className="text-xs text-muted-foreground">{voteBalance}</span>
            <Button
              disabled={isVoting}
              onClick={() => updateVote(false)}
              variant="ghost"
              size="sm"
              aria-label="Downvote"
              title="Downvote"
              className={clsx(
                "h-6 w-6 p-0",
                userVote === false
                  ? "text-primary"
                  : "text-muted-foreground hover:text-primary",
              )}
            >
              <ArrowDown className="h-3.5 w-3.5" />
            </Button>
          </div>
          <span className="text-xs text-muted-foreground">
            Was this review helpful?
          </span>
        </div>
        <div className="flex items-center justify-center gap-2">
          <span className="text-xs text-muted-foreground">Share:</span>

          <FacebookShareButton url={shareUrl}>
            <FacebookIcon size={24} round />
          </FacebookShareButton>

          <TwitterShareButton url={shareUrl} title={shareTitle}>
            <XIcon size={24} round />
          </TwitterShareButton>

          <LinkedinShareButton url={shareUrl} title={shareTitle}>
            <LinkedinIcon size={24} round />
          </LinkedinShareButton>
        </div>
      </div>
      <SignInRequiredDialog
        showDialog={showAuthDialog}
        setShowDialog={setShowAuthDialog}
        actionClick={handleLogin}
      />
    </main>
  );
};

export default ReviewPage;
