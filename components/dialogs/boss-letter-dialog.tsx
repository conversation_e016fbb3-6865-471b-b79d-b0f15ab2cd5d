import { FC } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";

type BossLetterDialogProps = {};

const BossLetterDialog: FC<BossLetterDialogProps> = ({}) => {
  return (
    <div className="space-y-6 text-base">
      <p>
        So, you clicked the link. Maybe you're here out of curiosity. Maybe
        you're sweating a little. Either way—welcome.
      </p>
      <p>
        This is your space. A mirror held up by the people you lead (or think
        you lead). It's not some corporate puff piece, not a highlight reel for
        your next promotion—just the raw, unfiltered truth from the people who
        work with you every day. Take a look around. Sit with it. Some of it
        might sting, some of it might make you proud. Either way, it's a
        gift—real, honest feedback that most bosses never get to hear.
      </p>
      <p>
        If you're here to escalate because you don't like what's being said,
        you're missing the point. This isn't about stroking egos or crafting PR
        spin. This is about self-awareness, growth, and, yeah—maybe a little bit
        of reckoning.
      </p>
      <p>
        That said, we've got rules. If you see something that violates our
        policies—private or sensitive information, something defamatory—flag it,
        and we'll take a look. But if the reviews just hurt your feelings? Maybe
        it's time to reflect instead of react.
      </p>
      <p>
        So go ahead. Claim your page. Own your reputation. And if you don't like
        what you see? Do something about it.
      </p>
      <p className="text-muted-foreground italic">— The Amplify Team</p>
    </div>
  );
};

export default BossLetterDialog;
