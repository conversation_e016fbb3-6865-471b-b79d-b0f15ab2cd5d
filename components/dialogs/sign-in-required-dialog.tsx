import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import React, { Dispatch, FC, SetStateAction } from "react";

type SignInRequiredDialogProps = {
  showDialog: boolean;
  setShowDialog: Dispatch<SetStateAction<boolean>>;
  actionClick: () => void;
  title?: string;
  description?: string;
};

const SignInRequiredDialog: FC<SignInRequiredDialogProps> = ({
  showDialog,
  setShowDialog,
  actionClick,
  title = "Sign in required",
  description = "Please sign in to submit your review. Your rating will be saved and you can continue where you left off.",
}) => {
  const handleSignInClick = () => {
    actionClick()
    setShowDialog(false)
  }

  return (
    <Dialog open={showDialog} onOpenChange={setShowDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter className={'gap-2'}>
          <Button variant="outline" onClick={() => setShowDialog(false)}>
            Cancel
          </Button>
          <Button onClick={handleSignInClick}>Sign In</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SignInRequiredDialog;
