'use client';

import { useEffect } from 'react';
import Clarity from '@microsoft/clarity';

const CLARITY_PROJECT_ID = "ra8mw89vyk";

export default function ClarityProvider() {
  useEffect(() => {
    // Initialize Clarity only on the client side
    if (typeof window !== 'undefined') {
      Clarity.init(CLARITY_PROJECT_ID);
      
      // Optional: Identify user if needed
      // Clarity.identify("custom-id", "custom-session-id", "custom-page-id", "friendly-name");
    }
  }, []);

  return null; // This component doesn't render anything
}
