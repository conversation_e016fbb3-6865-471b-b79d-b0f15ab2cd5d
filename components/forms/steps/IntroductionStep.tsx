'use client';

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { 
  ClipboardCheck, 
  Shield, 
  MessageSquare, 
  AlertTriangle,
  Megaphone,
  Target
} from 'lucide-react';

interface IntroductionStepProps {
  bossName: string;
  onComplete: (stepData: any) => void;
}

export default function IntroductionStep({ bossName, onComplete }: IntroductionStepProps) {
  return (
    <div className="px-2 md:px-4 py-4">
      <div className="space-y-6">
        {/* Header Section - More compact */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl font-medium">
            Make Your Voice Heard
          </h1>
          <p className="text-muted-foreground">
            Share your real experiences— no filter, just tell the honest truth.
          </p>
        </div>

        {/* Main Content - Grid layout for larger screens */}
        <div className="grid sm:grid-cols-2 gap-4">
          <div className="flex items-start gap-3">
            <MessageSquare className="h-5 w-5 text-primary mt-1 shrink-0" />
            <div>
              <h3 className="font-medium mb-1">Share Your Experience</h3>
              <p className="text-sm text-muted-foreground">
                Whether positive or needs improvement, we want to hear it all.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <Target className="h-5 w-5 text-primary mt-1 shrink-0" />
            <div>
              <h3 className="font-medium mb-1">Keep it Real</h3>
              <p className="text-sm text-muted-foreground">
                Focus on specific examples and real situations.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <Megaphone className="h-5 w-5 text-primary mt-1 shrink-0" />
            <div>
              <h3 className="font-medium mb-1">No Sugarcoating</h3>
              <p className="text-sm text-muted-foreground">
                Share the real story, good or bad.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <Shield className="h-5 w-5 text-primary mt-1 shrink-0" />
            <div>
              <h3 className="font-medium mb-1">Your Voice is Protected</h3>
              <p className="text-sm text-muted-foreground">
                Your feedback is completely anonymous.
              </p>
            </div>
          </div>
        </div>

        {/* Guidelines - More compact */}
        <div className="bg-muted/50 rounded-lg p-3">
          <div className="flex items-start gap-2 text-sm">
            <AlertTriangle className="h-4 w-4 text-primary mt-1" />
            <p className="text-muted-foreground">
              Keep it professional and constructive. Check our 
              <a href="/guidelines#posting" className="text-primary hover:underline ml-1">
                posting guidelines
              </a> for tips.
            </p>
          </div>
        </div>

        {/* CTA Section - More compact */}
        <div className="flex flex-col items-center gap-3">
          <Button 
            onClick={() => onComplete({})}
            size="lg"
            className="px-8"
          >
            Let's do this
          </Button>
          <p className="text-xs text-center text-muted-foreground">
            By continuing, you agree to our community guidelines
          </p>
        </div>
      </div>
    </div>
  );
} 