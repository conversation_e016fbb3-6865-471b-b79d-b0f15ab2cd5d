"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import {
  ChevronDown,
  Info,
  Shield,
  AlertTriangle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {BANNED_WORDS} from "@/utils/input-validator";

interface WrittenReviewStepProps {
  bossName: string;
  onComplete: (data: any, isSubmitting?: boolean) => void;
  initialData?: {
    headline?: string;
    content?: string;
  };
}

const MIN_HEADLINE_LENGTH = 10
const MIN_CONTENT_LENGTH = 50
const MAX_HEADLINE_LENGTH = 50
const MAX_CONTENT_LENGTH = 1000

export default function WrittenReviewStep({
  bossName,
  onComplete,
  initialData,
}: WrittenReviewStepProps) {
  const [headline, setHeadline] = useState(initialData?.headline || "");
  const [content, setContent] = useState(initialData?.content || "");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showGuidelines, setShowGuidelines] = useState(false);

  // Separate error states
  const [headlineError, setHeadlineError] = useState<string | null>(null);
  const [contentError, setContentError] = useState<string | null>(null);

  const contentLength = content?.length || 0;


  useEffect(() => {
    if (initialData?.headline !== headline) {
      setHeadline(initialData.headline || "");
    }
    if (initialData?.content !== content) {
      setContent(initialData.content || "");
    }
  }, [initialData]);

  useEffect(() => {
    const saveDraft = setTimeout(() => {
      onComplete(
        {
          review_headline: headline,
          review_content: content,
        },
        false,
      );
    }, 1000);

    return () => clearTimeout(saveDraft);
  }, [headline, content, onComplete]);

  const containsProfanity = (text: string) => {
    const lowerWords = text
      .toLowerCase()
      .replace(/[^\w\s]/g, "") // прибирає розділові знаки
      .split(/\s+/); // розбиває на слова

    return lowerWords.some((word) => BANNED_WORDS.includes(word));
  };

  const validateContent = (value: string) => {
    if (value.length < MIN_CONTENT_LENGTH) {
      return `At least ${MIN_CONTENT_LENGTH - value.length} more characters needed`;
    }
    if (value.length > MAX_CONTENT_LENGTH) {
      return `${value.length - MAX_CONTENT_LENGTH} characters over the limit`;
    }
    if (containsProfanity(value)) {
      return "Your review contains inappropriate or spammy language";
    }
    if (/https?:\/\/|www\./i.test(value)) {
      return "Links are not allowed in your review";
    }
    if (/<[^>]*>/g.test(value)) {
      return "HTML tags are not allowed in your review";
    }
    return null;
  };

  const validateHeadline = (value: string) => {
    if (value.length < MIN_HEADLINE_LENGTH) {
      return `At least ${MIN_HEADLINE_LENGTH - value.length} more characters needed`;
    }
    if (value.length > MAX_HEADLINE_LENGTH) {
      return `${value.length - MAX_HEADLINE_LENGTH} characters over the limit`;
    }
    if (containsProfanity(value)) {
      return "Your review contains inappropriate or spammy language";
    }
    if (/https?:\/\/|www\./i.test(value)) {
      return "Links are not allowed in your review";
    }
    if (/<[^>]*>/g.test(value)) {
      return "HTML tags are not allowed in your review";
    }
    return null;
  };

  const handleHeadlineChange = (value: string) => {
    setHeadline(value);
    setHeadlineError(validateHeadline(value));
  };

  const handleContentChange = (value: string) => {
    setContent(value);
    setContentError(validateContent(value));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const finalHeadlineError = validateHeadline(headline);
    const finalContentError = validateContent(content);

    const hasValidationErrors = !!finalHeadlineError || !!finalContentError

    setIsSubmitting(true);

    onComplete(
      {
        review_headline: headline.trim(),
        review_content: content.trim(),
        has_validation_errors: hasValidationErrors,
      },
      true,
    );
  };

  return (
    <div className="px-4 py-6 md:px-6">
      <div className="max-w-2xl mx-auto space-y-8">
        {/* Header */}
        <div className="space-y-2">
          <h2 className="text-xl font-semibold">
            Share your experience with {bossName}
          </h2>
          <p className="text-muted-foreground">
            Help others understand what it's really like to work with this boss
          </p>
        </div>

        {/* Review Form */}
        <div className="space-y-6">
          {/* Headline */}
          <div className="space-y-2">
            <Label htmlFor="headline">Review Headline</Label>
            <Input
              id="headline"
              placeholder="Summarize your experience in a few words..."
              value={headline}
              onChange={(e) => handleHeadlineChange(e.target.value)}
              maxLength={100}
              disabled={isSubmitting}
            />
            {headlineError && (
              <p className="text-xs text-red-500">{headlineError}</p>
            )}
          </div>

          {/* Main Review */}
          <div className="space-y-2">
            <Label htmlFor="content">Your Review</Label>
            <Textarea
              id="content"
              placeholder="What was it like working with this boss? Share specific examples..."
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              rows={8}
              className="resize-none text-base"
              disabled={isSubmitting}
            />
            <div className="flex items-center justify-between text-xs">
              <div
                className={cn(
                  "flex items-center gap-1",
                  contentError ? "text-red-500" : "text-muted-foreground",
                )}
              >
                {contentError ? (
                  <span>{contentError}</span>
                ) : (
                  <span>
                    {MAX_CONTENT_LENGTH - contentLength} characters remaining
                  </span>
                )}
              </div>
              <span className="text-muted-foreground">
                {contentLength}/{MAX_CONTENT_LENGTH}
              </span>
            </div>
          </div>
        </div>

        {/* Guidelines */}
        <motion.div
          initial={false}
          animate={{
            height: showGuidelines ? "auto" : "36px",
            opacity: 1,
          }}
          className="border rounded-lg overflow-hidden"
        >
          <div className="px-3 py-2">
            <div className="flex items-center justify-between">
              <button
                onClick={() => setShowGuidelines(!showGuidelines)}
                className="flex items-center gap-2 text-xs text-muted-foreground hover:text-foreground transition-colors"
                disabled={isSubmitting}
              >
                <Info className="h-3.5 w-3.5" />
                <span className="font-medium">
                  Review guidelines and policies
                </span>
                <ChevronDown
                  className={cn(
                    "h-3.5 w-3.5 transition-transform duration-200",
                    showGuidelines && "rotate-180",
                  )}
                />
              </button>
            </div>

            {showGuidelines && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-3 space-y-4 text-xs text-muted-foreground pb-2"
              >
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-primary">
                    <Shield className="h-3.5 w-3.5" />
                    <span className="font-medium">Confidentiality</span>
                  </div>
                  <p className="pl-5">
                    Your review will be posted anonymously. We remove
                    identifying information and moderate all reviews to protect
                    reviewer privacy.
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-primary">
                    <AlertTriangle className="h-3.5 w-3.5" />
                    <span className="font-medium">Content Guidelines</span>
                  </div>
                  <ul className="pl-5 space-y-1">
                    <li>• Focus on specific experiences and examples</li>
                    <li>• Be professional and constructive</li>
                    <li>• Avoid personal attacks or inappropriate language</li>
                    <li>• Include both strengths and areas for improvement</li>
                  </ul>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* Submit */}
        <div className="space-y-3 flex flex-col items-center">
          <Button
            onClick={handleSubmit}
            size="lg"
            className="px-12"
            disabled={isSubmitting || MIN_HEADLINE_LENGTH > headline.length  || MIN_CONTENT_LENGTH > content.length }
          >
            {isSubmitting ? "Submitting..." : "Submit Review"}
          </Button>
        </div>
      </div>
    </div>
  );
}
