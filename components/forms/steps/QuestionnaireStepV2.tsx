'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from "@/lib/utils";
import { 
  Brain, 
  LightbulbIcon, 
  MessageCircle, 
  Users, 
  Star, 
  Rocket,
  Check,
  Info,
  ChevronDown
} from 'lucide-react';
import {useMediaQuery} from "@/hooks/use-media-query";

interface Question {
  id: string;
  section: string;
  Icon: any;
  question: string;
  leftLabel: string;
  rightLabel: string;
  labels: Record<number, string>;
  helpText: Record<number, string>;
}

interface QuestionnaireStepV2Props {
  bossName: string;
  onComplete: (data: { questionnaire_answers: Record<string, number> }) => void;
  initialAnswers?: Record<string, number>;
}

const questions: Question[] = [
  {
    id: 'leadership_style',
    section: 'Leadership',
    Icon: Brain,
    question: "Let's be real: Is your boss a mentor who trusts you to get the job done, or are they micromanaging every move you make?",
    leftLabel: "Full-on Control Freak",
    rightLabel: "Total Mentor who Trusts",
    labels: {
      1: "Power-Obsessed Dictator",
      2: "Wildcard Boss",
      3: "Steady & Stable",
      4: "Calculated & Composed",
      5: "Visionary Strategist"
    },
    helpText: {
      1: "My way or the highway. Rules are rigid, input is ignored, and power is the priority.",
      2: "Swings between micromanaging and disappearing. No predictability, just chaos.",
      3: "Keeps things running smoothly without surprises or unnecessary panic.",
      4: "Doesn't rush into things. Thoughtful, measured, and keeps the team aligned.",
      5: "Always thinking ahead, connecting today's work to tomorrow's success."
    }
  },
  {
    id: 'decision_making',
    section: 'Decision-Making',
    Icon: LightbulbIcon,
    question: "Does your boss actually listen to input and adapt, or is it always their way or the highway?",
    leftLabel: "My Way or the Highway",
    rightLabel: "Collaborative and Flexible",
    labels: {
      1: "Iron-Fist Decision Maker",
      2: "Indecisive & Paralyzed",
      3: "By-the-Book Thinker",
      4: "Consistently Reliable",
      5: "Fast & Fearless"
    },
    helpText: {
      1: "No discussion, no collaboration—just orders to follow.",
      2: "Overthinks everything, delaying decisions and leaving the team stuck.",
      3: "Logical and steady, but don't expect bold or creative moves.",
      4: "Decisions are well-thought-out, fair, and keep the team on track.",
      5: "Moves quickly, makes bold calls, and keeps things progressing."
    }
  },
  {
    id: 'communication',
    section: 'Communication',
    Icon: MessageCircle,
    question: "Can you approach your boss with questions, or do they dodge conversations like it's an Olympic sport?",
    leftLabel: "Completely Unapproachable",
    rightLabel: "Super Approachable",
    labels: {
      1: "Corporate Black Hole",
      2: "Mixed Messages & Confusion",
      3: "Functional But Flat",
      4: "Clear & Direct",
      5: "Inspiring & Impactful"
    },
    helpText: {
      1: "Good luck getting updates or clear direction.",
      2: "Sometimes too much detail, sometimes not enough—rarely clear.",
      3: "Gets the job done but lacks clarity or engagement.",
      4: "Says what needs to be said, with no confusion or wasted words.",
      5: "Communicates with purpose, leaving people informed and energized."
    }
  },
  {
    id: 'team_dynamics',
    section: 'Team Dynamics',
    Icon: Users,
    question: "Does your boss boost team spirit or drain the energy out of the room?",
    leftLabel: "Energy Vampire",
    rightLabel: "Team Motivator",
    labels: {
      1: "Toxic & Divisive",
      2: "Team in Name Only",
      3: "Fine But Forgettable",
      4: "Aligned & Together",
      5: "High-Performing Machine"
    },
    helpText: {
      1: "Thrives on drama, favoritism, and backstabbing.",
      2: "No real collaboration, just individuals working in silos.",
      3: "People do their jobs, but there's no real team spirit.",
      4: "A boss who builds cohesion, creating a team that works well together.",
      5: "The team just clicks—collaborative, motivated, and effective."
    }
  },
  {
    id: 'feedback_style',
    section: 'Feedback and Recognition',
    Icon: Star,
    question: "Does your boss acknowledge your hard work, or do they only show up to point out mistakes?",
    leftLabel: "Only Critical",
    rightLabel: "Always Appreciative",
    labels: {
      1: "Feedback as Punishment",
      2: "More Draining Than Driving",
      3: "Bare Minimum Acknowledgment",
      4: "Fair & Encouraging",
      5: "Champion of Growth"
    },
    helpText: {
      1: "Praise? Never heard of it. Criticism comes fast and hard.",
      2: "Mostly negative, rarely uplifting, and never truly motivating.",
      3: "Performance reviews happen, but there's no real engagement.",
      4: "Gives balanced, useful feedback and makes recognition meaningful.",
      5: "Highlights strengths, pushes development, and makes people feel valued."
    }
  },
  {
    id: 'career_development',
    section: 'Professional Growth',
    Icon: Rocket,
    question: "Is your boss your biggest cheerleader for career growth, or are they the roadblock holding you back?",
    leftLabel: "Career Blocker",
    rightLabel: "Career Champion",
    labels: {
      1: "Career Dead End",
      2: "Occasional Effort, Mostly Absent",
      3: "You're on Your Own",
      4: "Supportive but Hands-Off",
      5: "Career-Accelerating Mentor"
    },
    helpText: {
      1: "No mentorship, no growth, no opportunities.",
      2: "Some training pops up, but structured development is missing.",
      3: "Doesn't block growth, but doesn't support it either.",
      4: "Opens doors and provides opportunities, but you have to take the initiative.",
      5: "Invests in their people, ensuring they grow and succeed."
    }
  }
];

export default function QuestionnaireStepV2({ 
  bossName, 
  onComplete,
  initialAnswers = {} 
}: QuestionnaireStepV2Props) {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const [answers, setAnswers] = useState<Record<string, number>>(initialAnswers);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(() => {
    const firstUnansweredIndex = questions.findIndex(q => !initialAnswers[q.id]);
    return firstUnansweredIndex === -1 ? questions.length - 1 : firstUnansweredIndex;
  });
  const [showInfo, setShowInfo] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAnswer = async (questionId: string, value: number) => {
    const newAnswers = { ...answers, [questionId]: value };
    setAnswers(newAnswers);
    
    // Auto-advance to next question after a short delay
    setTimeout(() => {
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(prev => prev + 1);
      } else {
        // Check if all questions are answered
        const allQuestionsAnswered = questions.every(q => newAnswers[q.id] !== undefined);
        if (allQuestionsAnswered) {
          setIsSubmitting(true);
          try {
            onComplete({ questionnaire_answers: newAnswers });
          } finally {
            setIsSubmitting(false);
          }
        }
      }
    }, 300);
  };

  const handleSubmit = () => {
    onComplete({ questionnaire_answers: answers });
  };

  const currentQuestion = questions[currentQuestionIndex];

  const getGradient = (value: number) => {
    if (value === 1) return "from-red-400 to-red-500";
    if (value === 2) return "from-orange-400 to-red-400";
    if (value === 3) return "from-yellow-400 to-yellow-500";
    if (value === 4) return "from-green-400 to-yellow-400";
    return "from-green-500 to-green-400";
  };

  const getLineGradient = (value: number) => {
    if (value === 1) return "bg-gradient-to-r from-red-400 to-red-500";
    if (value === 2) return "bg-gradient-to-r from-orange-400 to-red-400";
    if (value === 3) return "bg-gradient-to-r from-yellow-400 to-yellow-500";
    if (value === 4) return "bg-gradient-to-r from-green-400 to-yellow-400";
    return "bg-gradient-to-r from-green-500 to-green-400";
  };

  return (
      <div className="px-4 py-5 md:px-6 pb-24">
        <div className="max-w-2xl mx-auto">
        <motion.div
          key={currentQuestion.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="space-y-8"
        >
          {/* Question */}
          <p className="text-xl font-medium">
            {currentQuestion.question}
          </p>

          {/* Section Header with Description */}
          <div className="space-y-1 mb-4">
            <div className="flex items-center gap-3 text-primary">
              <currentQuestion.Icon className="h-5 w-5" />
              <h3 className="font-medium">{currentQuestion.section}</h3>
            </div>
            <p className="text-sm text-muted-foreground pl-8">
              Rate your boss's {currentQuestion.section.toLowerCase()} style
            </p>
          </div>

          {/* Scale */}
          <div className="grid grid-cols-5 gap-1">
            {[1, 2, 3, 4, 5].map((value) => {
              const isSelected = answers[currentQuestion.id] === value;
              const isEndPoint = value === 1 || value === 5;
              
              return (
                <div key={value} className="flex flex-col">
                  <motion.button
                    onClick={() => handleAnswer(currentQuestion.id, value)}
                    className={cn(
                      "relative py-5 rounded-xl transition-all",
                      "hover:bg-primary/5",
                      answers[currentQuestion.id] === value && "bg-primary/10"
                    )}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {/* Scale Point */}
                    <div className="absolute inset-x-0 top-2 flex justify-center">
                      <div 
                        className={cn(
                          "w-3 h-3 rounded-full",
                          "transition-all duration-200",
                          isSelected 
                            ? cn("bg-gradient-to-br shadow-sm", getGradient(value))
                            : cn("bg-gradient-to-br opacity-40", getGradient(value))
                        )}
                      />
                    </div>

                    {/* Scale Line */}
                    <div 
                      className={cn(
                        "absolute top-[13px] h-[2px]",
                        getLineGradient(value),
                        value === 1 ? "left-1/2" : value === 5 ? "right-1/2" : "",
                        isSelected ? "opacity-100" : "opacity-40"
                      )}
                      style={{
                        width: value === 1 || value === 5 ? '50%' : '100%'
                      }}
                    />
                  </motion.button>

                  {/* Label */}
                  <div className={cn(
                    "mt-3 text-xs text-center transition-colors duration-200",
                    isSelected && "font-medium",
                    isEndPoint ? (
                      value === 1 ? "text-red-500 font-medium" : "text-green-500 font-medium"
                    ) : (
                      "text-muted-foreground"
                    )
                  )}>
                    {currentQuestion.labels[value as keyof typeof currentQuestion.labels]}
                  </div>
                  
                  {/* Help Text - only show for selected value */}
                  {isSelected && isDesktop && (
                    <div className="mt-1 text-xs text-center text-muted-foreground px-2">
                      {currentQuestion.helpText?.[value as keyof typeof currentQuestion.helpText]}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* How to Rate Section - More subtle and contextual */}
          <motion.div
            initial={false}
            animate={{ 
              height: showInfo ? "auto" : "36px",
              opacity: 1
            }}
            className="border rounded-lg overflow-hidden"
          >
            <div className="px-3 py-2">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => setShowInfo(!showInfo)}
                  className="flex items-center gap-2 text-xs text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Info className="h-3.5 w-3.5" />
                  <span className="font-medium">How to rate this question</span>
                  <ChevronDown 
                    className={cn(
                      "h-3.5 w-3.5 transition-transform duration-200",
                      showInfo && "rotate-180"
                    )} 
                  />
                </button>
              </div>
              
              {showInfo && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="mt-2 space-y-2 text-xs text-muted-foreground pb-1"
                >
                  <p>Choose the description that best matches your experience:</p>
                  <div className="grid grid-cols-3 gap-2 pl-2">
                    <div>
                      <span className="text-red-500 font-medium block mb-1">1-2: Left Statement</span>
                      Strong agreement with the statement on the left
                    </div>
                    <div>
                      <span className="text-yellow-500 font-medium block mb-1">3: Neutral</span>
                      Mixed feelings or balanced between both
                    </div>
                    <div>
                      <span className="text-green-500 font-medium block mb-1">4-5: Right Statement</span>
                      Strong agreement with the statement on the right
                    </div>
                  </div>
                  <div className="pt-2 border-t mt-3">
                    <p>• Consider specific examples from your experience</p>
                    <p>• Focus on consistent patterns rather than one-off incidents</p>
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Progress Footer */}
      <div className="fixed mt-4 md:mt-0 bottom-0 left-0 right-0 border-t bg-background p-4">
        <div className="flex justify-center gap-4 max-w-2xl mx-auto">
          {questions.map((question, index) => {
            const Icon = question.Icon;
            const isCompleted = answers[question.id] !== undefined;
            const isCurrent = index === currentQuestionIndex;
            
            return (
              <div key={question.id} className="group relative">
                <button
                  onClick={() => setCurrentQuestionIndex(index)}
                  className={cn(
                    "relative w-10 h-10 rounded-full flex items-center justify-center transition-all",
                    isCurrent && "bg-primary/10",
                    !isCompleted && !isCurrent && "text-muted-foreground",
                    "hover:bg-primary/5"
                  )}
                >
                  <Icon className="h-4 w-4" />
                  {isCompleted && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="absolute inset-0 bg-primary/10 rounded-full animate-in fade-in-0 duration-300" />
                      <div className="relative z-10 bg-primary text-primary-foreground rounded-full p-1.5 shadow-sm animate-in zoom-in-50 duration-300">
                        <Check className="h-3.5 w-3.5" />
                      </div>
                    </div>
                  )}
                </button>
                {/* Tooltip */}
                <div className={cn(
                  "absolute bottom-full mb-2 left-1/2 -translate-x-1/2",
                  "opacity-0 group-hover:opacity-100 transition-opacity",
                  "pointer-events-none",
                  "whitespace-nowrap",
                  "bg-popover text-popover-foreground",
                  "text-xs font-medium",
                  "px-2 py-1 rounded shadow-sm",
                  "border"
                )}>
                  {question.section}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
} 