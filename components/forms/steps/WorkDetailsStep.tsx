'use client';

import React, { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { User2, Building2, Briefcase, Check } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from "@/lib/utils";
import type { WorkExperience } from '@/types';
import {Button} from "@/components/ui/button";
import {clsx} from "clsx";
import CompanyAutocomplete from "@/components/company-autocomplete";
import { validateInput, ValidationOptions } from '@/utils/input-validator';

interface WorkDetailsStepProps {
  bossName: string;
  workExperiences: WorkExperience[];
  onComplete: (data: {
    relationship: string;
    company: string;
    role: string;
    experience_id: number;
  }) => void;
  initialData?: {
    relationship?: string;
    company?: string;
    role?: string;
    experience_id?: number;
  };
}

// Define validation options as constants
const COMPANY_VALIDATION_OPTIONS: ValidationOptions = {
  minLength: 3,
  maxLength: 50,
  allowHtml: false,
  allowUrls: false,
  allowSpecialChars: false,
  required: true
};

const ROLE_VALIDATION_OPTIONS: ValidationOptions = {
  minLength: 3,
  maxLength: 50,
  allowHtml: false,
  allowUrls: false,
  allowSpecialChars: false,
  required: true
};

const relationshipOptions = [
  { value: "Direct Report", label: "Direct Report", description: "You reported directly to this boss" },
  { value: "Co-worker", label: "Co-worker", description: "You worked alongside this person" },
  { value: "Direct Manager/Supervisor", label: "Direct Manager/Supervisor", description: "This person reported directly to you" }
];

const STEPS = [
  { id: 1, label: "Relationship", Icon: User2 },
  { id: 2, label: "Company", Icon: Building2 },
  { id: 3, label: "Role", Icon: Briefcase }
];

export default function WorkDetailsStep({
  bossName,
  workExperiences,
  onComplete,
  initialData = {}
}: WorkDetailsStepProps) {

  console.log("===> WORK EXPERIENCES", workExperiences)
  console.log("===> INITIAL DATA", initialData)

  const [relationship, setRelationship] = React.useState(initialData?.relationship || '');

  const [selectedCompany, setSelectedCompany] = useState('');
  const [customCompanyInput, setCustomCompanyInput] = useState<string>('');
  const [companyError, setCompanyError] = useState<string | null>(null);

  const [selectedRole, setSelectedRole] = React.useState('');
  const [customRoleInput, setCustomRoleInput] = useState<string>('');
  const [roleError, setRoleError] = useState<string | null>(null);

  const bossCompanies = Array.from(new Set(workExperiences.map(exp => exp.company?.name)))

  // PREPARE SAVED DATA
  useEffect(() => {
    const initialCompany = initialData?.company
    if (!bossCompanies.includes(initialCompany)) {
      setCustomCompanyInput(initialCompany || '')
    }
    setSelectedCompany(initialCompany || '')

    const initialRole = initialData?.role
    const rolesJotTitles = workExperiences.map((workExperience) => workExperience.job_title)
    if (!rolesJotTitles.includes(initialRole)) {
      setCustomRoleInput(initialRole || '')
    }
    setSelectedRole(initialRole || '')
  }, []);

  const [step, setStep] = React.useState(() => {
    if (initialData?.role) return 3;
    if (initialData?.company) return 2;
    if (initialData?.relationship) return 2;
    return 1;
  });

  // Helper function to validate company input
  const validateCompanyInput = (value: string): string | null => {
    const result = validateInput(value, COMPANY_VALIDATION_OPTIONS);
    return result.valid ? null : result.message || null;
  };

  // Helper function to validate role input
  const validateRoleInput = (value: string): string | null => {
    const result = validateInput(value, ROLE_VALIDATION_OPTIONS);
    return result.valid ? null : result.message || null;
  };

  const handleCustomCompanyInputChange = (value: string) => {
    setSelectedCompany(value);
    setCustomCompanyInput(value);
    setCompanyError(validateCompanyInput(value));
  }

  const handleCustomCompanyInputClick = () => {
    setSelectedCompany(customCompanyInput);
  }

  const handleCustomRoleInputChange = (value: string) => {
    setSelectedRole(value);
    setCustomRoleInput(value);
    setRoleError(validateRoleInput(value));
  }

  const handleCustomRoleInputClick = () => {
    setSelectedRole(customRoleInput);
  }

  // Check if inputs are valid
  const isCompanyValid = () => {
    if (selectedCompany === customCompanyInput) {
      return !companyError && selectedCompany.trim().length >= COMPANY_VALIDATION_OPTIONS.minLength!;
    } else {
      return true
    }
  }
  const isRoleValid = () => {
    if (selectedRole === customRoleInput) {
      return !roleError && selectedRole.trim().length >= ROLE_VALIDATION_OPTIONS.minLength!
    } else {
      return true
    }
  };

  // Simple validation for relationship selection
  const isValidRelationship = (value: string) => value.trim().length >= 3;

  const handleRelationshipSelect = (value: string) => {
    setRelationship(value);
    if (isValidRelationship(value)) {
      setTimeout(() => setStep(2), 300);
    }
  };

  const handleCompanySelect = () => {
    if (isCompanyValid()) {
      setTimeout(() => setStep(3), 300);
    }
  };

  const handleRoleSelect = () => {
    if (!isRoleValid()) return;

    // Find the matching experience to get the experience_id
    const experience = workExperiences.find(exp =>
      exp.job_title === selectedRole &&
      exp.company?.name === selectedCompany
    );

    // Use a default experience_id of 0 if no matching experience is found
    // The backend will handle creating a new experience if needed
    const experienceId = experience ? (experience as any).experience_id || 0 : 0;

    setTimeout(() => {
      onComplete({
        relationship,
        company: selectedCompany,
        role: selectedRole,
        experience_id: experienceId
      });
    }, 300);
  };

  return (
      <div className="px-2 py-5 md:px-6 pb-24">
        <div className="max-w-2xl mx-auto max-h-full md:max-h-[400px]">
        <AnimatePresence mode="wait">
          {step === 1 && (
            <motion.div
              key="relationship"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              <Label className="text-lg font-semibold">
                What was your relationship with {bossName}?
              </Label>
              <div className="grid gap-3">
                {relationshipOptions.map((option) => (
                  <motion.div
                    key={option.value}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                  >
                    <button
                      type="button"
                      onClick={() => handleRelationshipSelect(option.value)}
                      className={cn(
                        "w-full p-3 rounded-lg border-2 text-left transition-colors",
                        relationship === option.value
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                      )}
                    >
                      <div className="flex items-center gap-3">
                        <User2 className={cn(
                          "h-4 w-4 flex-shrink-0",
                          relationship === option.value ? "text-primary" : "text-muted-foreground"
                        )} />
                        <div className="min-w-0 flex-1">
                          <div className="text-sm font-medium truncate">{option.label}</div>
                          <div className="text-xs text-muted-foreground truncate">{option.description}</div>
                        </div>
                      </div>
                    </button>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {step === 2 && (
            <motion.div
              key="company"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              <Label className="text-lg font-semibold">
                Where did you work together?
              </Label>
              <div className="grid gap-3">
                {bossCompanies
                  .filter((name): name is string => !!name)
                  .map(companyName => (
                    <motion.div
                      key={companyName}
                      whileHover={{ scale: 1.01 }}
                      whileTap={{ scale: 0.99 }}
                    >
                      <button
                        type="button"
                        onClick={() => setSelectedCompany(companyName)}
                        className={cn(
                          "w-full p-3 rounded-lg border-2 text-left transition-colors",
                          selectedCompany === companyName
                            ? "border-primary bg-primary/5"
                            : "border-border hover:border-primary/50"
                        )}
                      >
                        <div className="flex items-center gap-3">
                          <Building2 className={cn(
                            "h-4 w-4 min-h-4 flex-shrink-0",
                            selectedCompany === companyName ? "text-primary" : "text-muted-foreground"
                          )} />
                          <div className="text-sm font-medium line-clamp-2">{companyName}</div>
                        </div>
                      </button>
                    </motion.div>
                  ))}
              </div>
              {/* Input for custom company with autocomplete */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-muted-foreground">
                  Or add a custom company
                </Label>
                <CompanyAutocomplete
                  value={customCompanyInput}
                  onChange={handleCustomCompanyInputChange}
                  onClick={handleCustomCompanyInputClick}
                  placeholder="Enter company name (max 50 characters)"
                  maxLength={50}
                  className={clsx(
                    selectedCompany === customCompanyInput && "border-primary",
                      selectedCompany === customCompanyInput && companyError && "border-destructive"
                  )}
                />
                {companyError && (
                  <p className="text-xs text-destructive">
                    {companyError}
                  </p>
                )}
              </div>
              <div className="pb-0 md:pb-16">
                <Button
                  type="button"
                  onClick={handleCompanySelect}
                  className="w-full"
                  disabled={!isCompanyValid()}
                >
                  Next
                </Button>
              </div>
            </motion.div>
          )}

          {step === 3 && (
            <motion.div
              key="role"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              <Label className="text-lg font-semibold">
                What was {bossName}'s role at {selectedCompany}?
              </Label>
              <div className="grid gap-3">
                {workExperiences
                  .filter(exp => exp.company?.name === selectedCompany)
                  .map(exp => exp.job_title)
                  .filter((role): role is string => !!role)
                  .map(role => (
                    <motion.div
                      key={role}
                      whileHover={{ scale: 1.01 }}
                      whileTap={{ scale: 0.99 }}
                    >
                      <button
                        type="button"
                        onClick={() => setSelectedRole(role)}
                        className={cn(
                          "w-full p-3 rounded-lg border-2 text-left transition-colors",
                          selectedRole === role
                            ? "border-primary bg-primary/5"
                            : "border-border hover:border-primary/50"
                        )}
                      >
                        <div className="flex items-center gap-3">
                          <User2 className={cn(
                            "h-4 w-4 flex-shrink-0",
                            selectedRole === role ? "text-primary" : "text-muted-foreground"
                          )} />
                          <div className="text-sm font-medium line-clamp-2">{role}</div>
                        </div>
                      </button>
                    </motion.div>
                  ))}
              </div>
              {/* Input for custom role */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-muted-foreground">
                  Or add a custom role
                </Label>
                <input
                    type="text"
                    value={customRoleInput}
                    onChange={(e) => handleCustomRoleInputChange(e.target.value)}
                    onClick={handleCustomRoleInputClick}
                    placeholder="Enter boss role (max 50 characters)"
                    maxLength={50}
                    className={clsx(
                        "w-full p-3 rounded-lg border-2 border-border focus:border-primary focus:outline-none transition-colors text-base font-medium",
                        selectedRole === customRoleInput && "border-primary",
                        selectedRole === customRoleInput && roleError && "border-destructive"
                    )}/>
                {roleError && (
                  <p className="text-xs text-destructive">
                    {roleError}
                  </p>
                )}
              </div>
              <div className="pb-0 md:pb-16">
                <Button
                  type="button"
                  onClick={handleRoleSelect}
                  className="w-full"
                  disabled={!isRoleValid()}
                >
                  Next
                </Button>
                {/*{selectedRole && !isRoleValid() && (*/}
                {/*  <p className="text-xs text-destructive text-center">*/}
                {/*    Role must be at least 3 characters*/}
                {/*  </p>*/}
                {/*)}*/}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Progress Footer */}
      <div className="fixed mt-4 md:mt-0 bottom-0 left-0 right-0 border-t bg-background p-4">
        <div className="flex justify-center gap-4 max-w-2xl mx-auto">
          {STEPS.map((stepItem) => {
            const Icon = stepItem.Icon;
            const isCompleted = step > stepItem.id;
            const isCurrent = step === stepItem.id;

            return (
              <div key={stepItem.id} className="group relative">
                <button
                  onClick={() => {
                    // Only allow navigation to completed steps or current step
                    if (step >= stepItem.id) {
                      setStep(stepItem.id);
                    }
                  }}
                  className={cn(
                    "relative w-10 h-10 rounded-full flex items-center justify-center transition-all",
                    isCurrent && "bg-primary/10",
                    !isCompleted && !isCurrent && "text-muted-foreground",
                    "hover:bg-primary/5"
                  )}
                >
                  <Icon className="h-4 w-4" />
                  {isCompleted && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="absolute inset-0 bg-primary/10 rounded-full animate-in fade-in-0 duration-300" />
                      <div className="relative z-10 bg-primary text-primary-foreground rounded-full p-1.5 shadow-sm animate-in zoom-in-50 duration-300">
                        <Check className="h-3.5 w-3.5" />
                      </div>
                    </div>
                  )}
                </button>
                {/* Tooltip */}
                <div className={cn(
                  "absolute bottom-full mb-2 left-1/2 -translate-x-1/2",
                  "opacity-0 group-hover:opacity-100 transition-opacity",
                  "pointer-events-none",
                  "whitespace-nowrap",
                  "bg-popover text-popover-foreground",
                  "text-xs font-medium",
                  "px-2 py-1 rounded shadow-sm",
                  "border"
                )}>
                  {stepItem.label}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}