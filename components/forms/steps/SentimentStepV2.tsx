'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { MiddleFingerIcon } from '@/components/icons/MiddleFingerIcon';
import { HeartHandsIcon } from '@/components/icons/HeartHandsIcon';
import { ThumbsUpIcon } from '@/components/icons/ThumbsUpIcon';
import { ThumbsDownIcon } from '@/components/icons/ThumbsDownIcon';
import { ThumbHorizontalIcon } from '@/components/icons/ThumbHorizontalIcon';
import { cn } from "@/lib/utils";
import SignInRequiredDialog from "@/components/dialogs/sign-in-required-dialog";

interface SentimentStepV2Props {
  bossId: string;
  bossName: string;
  onComplete: (data: { sentiment: number; sentimentId: number }) => void;
  initialSentiment?: number;
}

const sentimentOptions = [
  {
    value: -2,
    Icon: MiddleFingerIcon,
    label: "Very Negative",
    description: "Toxic leadership, avoid at all costs",
    bgColor: "bg-red-50 dark:bg-red-500/10",
    hoverBgColor: "hover:bg-red-100 dark:hover:bg-red-500/20",
    iconColor: "text-red-500",
    borderColor: "group-hover:border-red-500"
  },
  {
    value: -1,
    Icon: ThumbsDownIcon,
    label: "Negative",
    description: "Poor management style",
    bgColor: "bg-orange-50 dark:bg-orange-500/10",
    hoverBgColor: "hover:bg-orange-100 dark:hover:bg-orange-500/20",
    iconColor: "text-orange-500",
    borderColor: "group-hover:border-orange-500"
  },
  {
    value: 0,
    Icon: ThumbHorizontalIcon,
    label: "Neutral",
    description: "Average leadership",
    bgColor: "bg-yellow-50 dark:bg-yellow-500/10",
    hoverBgColor: "hover:bg-yellow-100 dark:hover:bg-yellow-500/20",
    iconColor: "text-yellow-500",
    borderColor: "group-hover:border-yellow-500"
  },
  {
    value: 1,
    Icon: ThumbsUpIcon,
    label: "Positive",
    description: "Good boss to work for",
    bgColor: "bg-green-50 dark:bg-green-500/10",
    hoverBgColor: "hover:bg-green-100 dark:hover:bg-green-500/20",
    iconColor: "text-green-500",
    borderColor: "group-hover:border-green-500"
  },
  {
    value: 2,
    Icon: HeartHandsIcon,
    label: "Very Positive",
    description: "Exceptional leadership",
    bgColor: "bg-pink-50 dark:bg-pink-500/10",
    hoverBgColor: "hover:bg-pink-100 dark:hover:bg-pink-500/20",
    iconColor: "text-pink-500",
    borderColor: "group-hover:border-pink-500"
  }
];

export default function SentimentStepV2({ 
  bossId, 
  bossName, 
  onComplete,
  initialSentiment 
}: SentimentStepV2Props) {
  const [showAuthDialog, setShowAuthDialog] = React.useState(false);
  const [pendingSentiment, setPendingSentiment] = useState<number | null>(initialSentiment ?? null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const handleSentimentClick = async (value: number) => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      setPendingSentiment(value);
      setShowAuthDialog(true);
      return;
    }

    setIsSubmitting(true);
    try {
      const { data: sentimentData, error: sentimentError } = await supabase
        .from('sentiments')
        .insert({ 
          boss_id: bossId,
          sentiment: value,
          reviewer_id: user.id,
          created_at: new Date().toISOString()
        })
        .select('sentiment_id')
        .single();

      if (sentimentError) throw sentimentError;
      if (!sentimentData) throw new Error('No sentiment data returned');

      onComplete({ 
        sentiment: value, 
        sentimentId: sentimentData.sentiment_id 
      });
    } catch (error) {
      console.error('Error saving sentiment:', error);
      // You might want to show an error message to the user here
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogin = () => {
    const currentPath = window.location.pathname;
    router.push(`/sign-in?redirect=${encodeURIComponent(currentPath)}`);
  };

  return (
    <div className="px-2 py-6 md:px-6 min-h-full">
      <div className="max-w-2xl mx-auto">
        <h2 className="text-xl font-semibold mb-2">Rate your experience with {bossName}</h2>
        <p className="text-muted-foreground mb-8">How would you describe your overall experience?</p>

        <div className="grid grid-cols-1 sm:grid-cols-5 gap-6">
          {sentimentOptions.map((option) => {
            const Icon = option.Icon;
            return (
              <motion.div
                key={option.value}
                className="group flex flex-col items-center text-center"
                whileTap={{ scale: 0.98 }}
              >
                <button
                  onClick={() => handleSentimentClick(option.value)}
                  className={cn(
                    "w-20 h-20 rounded-full",
                    "flex items-center justify-center",
                    "transition-all duration-200",
                    option.bgColor,
                    option.hoverBgColor,
                    "border-2",
                    pendingSentiment === option.value 
                      ? "border-primary" 
                      : "border-transparent"
                  )}
                >
                  <Icon className={cn("w-12 h-12", option.iconColor)} />
                </button>
                <div className="mt-4 space-y-1">
                  <span className="block font-medium text-sm">{option.label}</span>
                  <span className="block text-xs text-muted-foreground">
                    {option.description}
                  </span>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
      <SignInRequiredDialog showDialog={showAuthDialog} setShowDialog={setShowAuthDialog} actionClick={handleLogin}/>
    </div>
  );
} 