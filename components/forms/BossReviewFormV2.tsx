'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, Check } from 'lucide-react';
import { cn } from "@/lib/utils";
import SentimentStepV2 from './steps/SentimentStepV2';
import WorkDetailsStep from './steps/WorkDetailsStep';
import NPSSurveyStep from './steps/NPSSurveyStep';
import QuestionnaireStepV2 from './steps/QuestionnaireStepV2';
import WrittenReviewStep from './steps/WrittenReviewStep';
import IntroductionStep from './steps/IntroductionStep';
import { createClient } from '@/utils/supabase/client';
import type { WorkExperience, Review } from '@/types';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {useRouter} from "next/navigation";
import {routes} from "@/utils/routes";

interface BossReviewFormV2Props {
  bossId: string;
  bossName: string;
  workExperiences: WorkExperience[];
  onClose: () => void;
  onSubmit: (review: Partial<Review>) => void;
  isDesktop?: boolean
}

const STEPS = [
  { id: 1, label: "Initial Rating" },
  { id: 2, label: "Work Details" },
  { id: 3, label: "Recommendation" },
  { id: 4, label: "Leadership Style" },
  { id: 5, label: "Written Review" }
];

interface FormData {
  sentiment?: number;
  sentimentId?: number;
  relationship?: string;
  company?: string;
  role?: string;
  experience_id?: number;
  nps_score?: number;
  questionnaire_answers?: Record<string, number>;
  review_headline: string;
  review_content: string;
}

const STORAGE_KEY = 'bossReview_'; // Will be concatenated with bossId

type ReviewStatus = 'published' | 'pending_review' | 'flagged' | 'approved' | 'rejected' | 'restricted';


export default function BossReviewFormV2({
  bossId,
  bossName,
  workExperiences,
  onClose,
  onSubmit,
    isDesktop = false
}: BossReviewFormV2Props) {
  const router = useRouter();

  //LOADING STATES
  const [isLoadingDraftData, setIsLoadingDraftData] = useState<boolean>(true);

  const [createdReviewId, setCreatedReviewId] = useState<string | null>(null);
  const [isNewReviewOnModeration, setIsNewReviewOnModeration] = useState<boolean>(false);

  // Load initial state from localStorage if it exists
  const [currentStep, setCurrentStep] = useState(() => {
    const saved = localStorage.getItem(`${STORAGE_KEY}${bossId}`);
    return saved ? JSON.parse(saved).currentStep : 1;
  });

  const [furthestStep, setFurthestStep] = useState(() => {
    const saved = localStorage.getItem(`${STORAGE_KEY}${bossId}`);
    return saved ? JSON.parse(saved).furthestStep : 1;
  });

  // const [currentStep, setCurrentStep] = useState(1)
  // const [furthestStep, setFurthestStep] = useState(1);

  const [formData, setFormData] = useState<FormData>(() => {
    const saved = localStorage.getItem(`${STORAGE_KEY}${bossId}`);
    if (saved) {
      const parsedData = JSON.parse(saved).formData;
      return {
        ...parsedData,
        review_headline: parsedData.review_headline || "",
        review_content: parsedData.review_content || "",
      };
    }
    return {
      review_headline: "",
      review_content: "",
    };
  });

  const [completedSteps, setCompletedSteps] = useState<number[]>(() => {
    const saved = localStorage.getItem(`${STORAGE_KEY}${bossId}`);
    return saved ? JSON.parse(saved).completedSteps : [];
  });

  const [sentimentId, setSentimentId] = useState<number | null>(() => {
    const saved = localStorage.getItem(`${STORAGE_KEY}${bossId}`);
    return saved ? JSON.parse(saved).sentimentId : null;
  });

  const [showIntroduction, setShowIntroduction] = useState(() => {
    const saved = localStorage.getItem(`${STORAGE_KEY}${bossId}`);
    return saved ? false : true;
  });

  const [showExitDialog, setShowExitDialog] = useState(false);

  const [draftId, setDraftId] = useState<number | null>(() => {
    const saved = localStorage.getItem(`${STORAGE_KEY}${bossId}`);
    return saved ? JSON.parse(saved).draftId : null;
  });

  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  // Add new state for the already reviewed dialog
  const [showAlreadyReviewedDialog, setShowAlreadyReviewedDialog] = useState(false);
  const [existingReviewUuid, setExistingReviewUuid] = useState<string>('')

  // Modify the initial useEffect to handle checks in the correct order
  useEffect(() => {
    const checkReviewStatus = async () => {
      setIsLoadingDraftData(true)
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) return;

      try {
        // First check for existing published review
        const { data: existingReview } = await supabase
          .from('reviews')
          .select('review_id, review_uuid')
          .eq('boss_id', bossId)
          .eq('reviewer_id', user.id)
          .single();

        if (existingReview) {
          // Immediately show dialog and don't load any drafts
          setShowAlreadyReviewedDialog(true);
          setExistingReviewUuid(existingReview.review_uuid)
          // Clear any existing form data and localStorage
          setFormData({ review_headline: '', review_content: '' });
          setCurrentStep(1);
          setFurthestStep(1);
          localStorage.removeItem(`${STORAGE_KEY}${bossId}`);
          return;
        }

        // Only if no published review exists, check for draft
        const { data: draftData } = await supabase
          .from('draft_reviews')
          .select('*')
          .eq('boss_id', bossId)
          .eq('reviewer_id', user.id)
          .single();

        if (draftData?.form_data) {
          setDraftId(draftData.draft_id);

          const updatedFormData: FormData = {
            ...draftData.form_data,
            review_headline: draftData.form_data.review_headline || '',
            review_content: draftData.form_data.review_content || ''
          };

          setFormData(updatedFormData);
          setCompletedSteps(Array.from({ length: draftData.furthest_step - 1 }, (_, i) => i + 1))
          setCurrentStep(draftData.furthest_step);
          setFurthestStep(draftData.furthest_step);
        }
      } catch (error) {
        console.error('Error checking review status:', error);
      } finally {
        setIsLoadingDraftData(false)
      }
    };

    // Reset form state before checking
    // setFormData({ review_headline: '', review_content: '' });
    // setCurrentStep(1);
    // setFurthestStep(1);
    // setShowIntroduction(true);

    checkReviewStatus();
  }, [bossId]);

  const saveToLocalStorage = (formDataToSave: FormData) => {
    try {
      const storageState = {
        currentStep,
        furthestStep,
        formData: formDataToSave,
        completedSteps: Array.from(completedSteps),
        sentimentId,
        lastUpdated: new Date().toISOString(),
        bossId,
        draftId
      };

      localStorage.setItem(
        `${STORAGE_KEY}${bossId}`,
        JSON.stringify(storageState)
      );
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  const saveDraftToSupabase = async (formData: FormData, currentStep: number, furthestStep: number) => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return;

    try {
      // Calculate timestamps once
      const now = new Date();
      const lastUpdated = now.toISOString();
      const expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString();

      // Prepare draft data
      const draftData = {
        boss_id: bossId,
        reviewer_id: user.id,
        form_data: formData,
        current_step: currentStep,
        furthest_step: furthestStep,
        last_updated: lastUpdated,
        expires_at: expiresAt
      };

      // Use upsert with ON CONFLICT to handle both insert and update in one operation
      const { data: upsertedDraft, error } = await supabase
        .from('draft_reviews')
        .upsert(
          draftData,
          {
            onConflict: 'boss_id,reviewer_id',
            ignoreDuplicates: false
          }
        )
        .select('draft_id')
        .single();

      if (error) throw error;

      // Update local draftId if we don't have one or if it changed
      if (upsertedDraft && (!draftId || draftId !== upsertedDraft.draft_id)) {
        setDraftId(upsertedDraft.draft_id);
      }
    } catch (error) {
      console.error('Error saving draft:', error);
      // Optionally, you could add user-facing error handling here
    }
  };

  // Save state to localStorage and Supabase
  useEffect(() => {
    try {
      if (!isLoadingDraftData) {
        // saveToLocalStorage(formData);
        saveDraftToSupabase(formData, currentStep, furthestStep);
      }
    } catch (error) {
      console.error('Error saving form state:', error);
    }
  }, [currentStep, furthestStep, formData, completedSteps, sentimentId, bossId, draftId, isLoadingDraftData]);

  const handleStepComplete = async (stepData: any) => {
    console.log('Step complete called with:', { stepData, currentStep, showIntroduction });  // Debug log

    // Special handling for introduction step
    if (showIntroduction) {
      console.log('Completing introduction step');  // Debug log
      setShowIntroduction(false);
      // setCurrentStep(1);
      return;
    }

    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return;

    try {
      // Update form data with the new step data
      const newFormData = {
        ...formData,
        ...stepData,
        ...(stepData.review_headline !== undefined && { review_headline: stepData.review_headline }),
        ...(stepData.review_content !== undefined && { review_content: stepData.review_content })
      };
      setFormData(newFormData);

      // Handle specific step data
      switch (currentStep) {
        case 1: // Sentiment Step
          if (stepData.sentimentId) {
            setSentimentId(stepData.sentimentId);
          }
          break;

        case 2: // Work Details Step
          if (stepData.experience_id) {
            // Update work experience if needed
            const { error: workError } = await supabase
              .from('work_experiences')
              .update({ is_verified: true })
              .eq('experience_id', stepData.experience_id);

            if (workError) throw workError;
          }
          break;

        case 3: // NPS Survey Step
          // NPS score will be following with final review
          break;

        case 4: // Questionnaire Step
          if (stepData.questionnaire_answers) {
            // Save questionnaire responses to draft_reviews for now
            // They will be properly following when the review is submitted
          }
          break;

        case 5: // Written Review Step
          if (draftId) {
            console.log('Saving written review data:', stepData);

            // Create updated form data
            const updatedFormData = {
              ...newFormData,
              review_headline: stepData.review_headline,
              review_content: stepData.review_content
            };

            // Save to Supabase
            const { error: draftError } = await supabase
              .from('draft_reviews')
              .update({
                form_data: updatedFormData,
                last_updated: new Date().toISOString()
              })
              .eq('draft_id', draftId);

            if (draftError) throw draftError;

            // Update local form data
            setFormData(updatedFormData);
            return; // Return early to prevent further updates
          }
          break;
      }

      // Update completed steps
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps(prev => [...prev, currentStep].sort((a, b) => a - b));
      }

      // Move to next step
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      setFurthestStep((prev: number) => Math.max(prev, nextStep));

    } catch (error) {
      console.error('Error saving step data:', error);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep((prev: number) => prev - 1);
    }
  };

  const handleSaveAndExit = () => {
    // Get the current form data including any in-progress written review
    const currentFormData = {
      ...formData,
      // Get the current values from the form state
      review_headline: (document.getElementById('headline') as HTMLInputElement)?.value || formData.review_headline,
      review_content: (document.getElementById('content') as HTMLTextAreaElement)?.value || formData.review_content
    };

    // Use the optimized helper functions
    const saveDraft = async () => {
      try {
        // Save to Supabase using the optimized function
        await saveDraftToSupabase(currentFormData, currentStep, furthestStep);
        // Update local storage with current form data
        // saveToLocalStorage(currentFormData);
      } catch (error) {
        console.error('Error saving draft:', error);
      }
    };

    // Save draft before showing exit dialog
    saveDraft().then(() => {
      setShowExitDialog(true);
    });
  };

  const handleConfirmExit = () => {
    // Save current state to localStorage using helper function
    // saveToLocalStorage(formData);
    setShowExitDialog(false);
    onClose();
  };

  // Modify handleFinalSubmit to require explicit submission
  const handleFinalSubmit = async (reviewData: any, isSubmitting: boolean = false) => {
    // If not explicitly submitting, just save the draft
    if (!isSubmitting) {
      setFormData(prev => ({
        ...prev,
        review_headline: reviewData.review_headline,
        review_content: reviewData.review_content
      }));
      return;
    }

    // Validate content before submission
    if (!reviewData.review_headline?.trim() || !reviewData.review_content?.trim()) {
      return;
    }

    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return;

    try {
      // Check for existing review first
      const { data: existingReview } = await supabase
        .from('reviews')
        .select('review_id')
        .eq('boss_id', bossId)
        .eq('reviewer_id', user.id)
        .eq('status', 'published')
        .single();

      if (existingReview) {
        // Show error message or handle duplicate submission
        console.error('Review already exists for this boss');
        return;
      }

      // Check if the selected company exists in the database
      let company_id;
      const { data: existingCompany } = await supabase
        .from('companies')
        .select('company_id')
        .ilike('name', formData.company || '')
        .single();

      if (existingCompany) {
        company_id = existingCompany.company_id;
      } else {
        // Create a new company if it doesn't exist
        const { data: newCompany, error: companyError } = await supabase
          .from('companies')
          .insert({
            name: formData.company,
            is_custom: true
          })
          .select('company_id')
          .single();

        if (companyError) throw companyError;
        company_id = newCompany.company_id;
      }

      // Check if we need to create a new work experience
      let experience_id = formData.experience_id;
      if (!experience_id || experience_id === 0) {
        // Create a new work experience
        const { data: newExperience, error: experienceError } = await supabase
          .from('work_experiences')
          .insert({
            boss_id: bossId,
            company_id: company_id,
            job_title: formData.role,
            user_id: user.id,
            is_custom: true
          })
          .select('experience_id')
          .single();

        if (experienceError) throw experienceError;
        experience_id = newExperience.experience_id;
      }

      // Create the review with only valid columns
      const { data: newReviewData, error: reviewError } = await supabase
        .from('reviews')
        .insert({
          boss_id: bossId,
          reviewer_id: user.id,
          sentiment_id: sentimentId,
          relationship: formData.relationship,
          experience_id: experience_id,
          company_id: company_id,
          nps_score: formData.nps_score,
          review_headline: reviewData.review_headline,
          review_content: reviewData.review_content,
          status: reviewData.has_validation_errors ? 'flagged' as ReviewStatus : 'published' as ReviewStatus,
          created_at: new Date().toISOString(),
          questionnaire_completed: true
        })
        .select('review_id, review_uuid, status' )
        .single();

      if (reviewError) throw reviewError;
      if (!newReviewData) throw new Error('No review data returned');

      // Update sentiment with review_id
      if (sentimentId) {
        const { error: sentimentError } = await supabase
          .from('sentiments')
          .update({ review_id: newReviewData.review_id })
          .eq('sentiment_id', sentimentId);

        if (sentimentError) throw sentimentError;
      }

      // Save questionnaire responses
      if (formData.questionnaire_answers && newReviewData) {
        const questionnaireResponses = Object.entries(formData.questionnaire_answers).map(([question_id, score]) => ({
          review_id: newReviewData.review_id,
          question_id,
          score,
          created_at: new Date().toISOString()
        }));

        const { error: questionnaireError } = await supabase
          .from('questionnaire_responses')
          .insert(questionnaireResponses);

        if (questionnaireError) throw questionnaireError;
      }

      // Delete draft if exists
      if (draftId) {
        const {error} = await supabase
          .from('draft_reviews')
          .delete()
          .eq('draft_id', draftId);
      }

      // Clear local storage
      localStorage.removeItem(`${STORAGE_KEY}${bossId}`);

      // Update success dialog content
      setCreatedReviewId(newReviewData.review_uuid)
      setShowSuccessDialog(true);
      setIsNewReviewOnModeration(newReviewData.status === "flagged")
    } catch (error) {
      console.error('Error submitting review:', error);
    }
  };

  // Optional: Add cleanup on unmount
  useEffect(() => {
    return () => {
      // Optionally clear old following reviews (e.g., older than 7 days)
      const cleanupOldSavedReviews = () => {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith(STORAGE_KEY)) {
            const saved = JSON.parse(localStorage.getItem(key) || '{}');
            const lastUpdated = new Date(saved.lastUpdated);
            const daysSinceUpdate = (new Date().getTime() - lastUpdated.getTime()) / (1000 * 3600 * 24);

            if (daysSinceUpdate > 7) {
              localStorage.removeItem(key);
            }
          }
        });
      };

      cleanupOldSavedReviews();
    };
  }, []);

  const isStepComplete = (stepId: number) => completedSteps.includes(stepId);

  const canAccessStep = (stepId: number) => stepId <= furthestStep;

  const handleSentimentSubmit = async (data: { sentiment: number; sentimentId: number }) => {
    const { sentiment, sentimentId: newSentimentId } = data;

    // Update the form data with the sentiment
    setFormData(prev => ({
      ...prev,
      sentiment: sentiment
    }));

    // Update the sentiment ID
    setSentimentId(newSentimentId);

    // Mark step as complete and move to next step
    if (!completedSteps.includes(1)) {
      setCompletedSteps(prev => [...prev, 1].sort((a, b) => a - b));
    }

    // Move to next step
    setCurrentStep(2);
    setFurthestStep((prev: number) => Math.max(prev, 2));
  };

  const handleGoToMyReview = async () => {
    router.push(routes.review(existingReviewUuid));
  }

  const handleGoToCreatedReview = () => {
    if (createdReviewId) router.push(routes.review(createdReviewId))
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex-none px-2 md:px-4 py-3 border-b">
        <div
          className={cn(
            "flex items-center justify-between",
            !showIntroduction && "mb-2",
          )}
        >
          <div className="flex items-center gap-2 md:gap-4">
            {!isDesktop && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="hover:bg-transparent p-1 md:p-2"
              >
                <ChevronLeft className="h-4 w-4 md:h-5 md:w-5" />
              </Button>
            )}
            <h2
              className="text-sm md:text-base font-semibold truncate max-w-[180px] md:max-w-none"
              title={`Review ${bossName}`}
            >
              Review {bossName}
            </h2>
          </div>
          {!showIntroduction && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSaveAndExit}
              className="text-xs md:text-sm px-2 md:px-3"
            >
              Save & Exit
            </Button>
          )}
        </div>

        {/* Progress Steps - Only show when past introduction */}
        {!showIntroduction && (
          <div className="px-0 md:px-4">
            <div className="relative flex justify-between w-full">
              {/* Progress line connecting steps */}
              <div className="absolute top-4 left-0 right-0 h-0.5 bg-muted -z-10" />

              {STEPS.map((step) => {
                const isComplete = isStepComplete(step.id);
                const isCurrent = step.id === currentStep;
                const canNavigate = canAccessStep(step.id);

                return (
                  <div key={step.id} className="flex flex-col items-center">
                    <button
                      onClick={() => canNavigate && setCurrentStep(step.id)}
                      disabled={!canNavigate}
                      className={cn(
                        "w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center",
                        "text-xs md:text-sm font-medium border-2 transition-all",
                        "relative bg-background",
                        isComplete && "border-primary text-primary",
                        isCurrent && "border-primary text-primary",
                        !isComplete &&
                          !isCurrent &&
                          "border-muted text-muted-foreground",
                        canNavigate && "hover:scale-105 cursor-pointer",
                        !canNavigate && "cursor-default",
                      )}
                    >
                      {isComplete ? (
                        <Check className="h-3 w-3 md:h-4 md:w-4" />
                      ) : (
                        <span>{step.id}</span>
                      )}
                    </button>

                    <div className="mt-1 md:mt-2 text-center max-w-[60px] md:max-w-none">
                      <div
                        className={cn(
                          "text-[10px] md:text-xs font-medium truncate",
                          isComplete || isCurrent || canNavigate
                            ? "text-primary"
                            : "text-muted-foreground",
                        )}
                        title={step.label} // Add tooltip for truncated text
                      >
                        {step.label}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        <AnimatePresence mode="wait">
          <motion.div
            key={showIntroduction ? "intro" : currentStep}
            initial={{ x: 300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -300, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className={cn(
              "min-h-[calc(100vh-116px)] md:min-h-fit",
              showIntroduction && "min-h-[calc(100vh-65px)] md:min-h-fit",
            )}
          >
            {showIntroduction ? (
              <IntroductionStep
                bossName={bossName}
                onComplete={() => {
                  console.log("Introduction complete called"); // Debug log
                  handleStepComplete({});
                }}
              />
            ) : (
              <>
                {currentStep === 1 && (
                  <SentimentStepV2
                    bossId={bossId}
                    bossName={bossName}
                    onComplete={handleSentimentSubmit}
                    initialSentiment={formData.sentiment}
                  />
                )}
                {currentStep === 2 && (
                  <WorkDetailsStep
                    bossName={bossName}
                    workExperiences={workExperiences}
                    onComplete={handleStepComplete}
                    initialData={{
                      relationship: formData.relationship,
                      company: formData.company,
                      role: formData.role,
                    }}
                  />
                )}
                {currentStep === 3 && (
                  <NPSSurveyStep
                    bossName={bossName}
                    onComplete={handleStepComplete}
                    initialScore={formData.nps_score}
                  />
                )}
                {currentStep === 4 && (
                  <QuestionnaireStepV2
                    bossName={bossName}
                    onComplete={handleStepComplete}
                    initialAnswers={formData.questionnaire_answers}
                  />
                )}
                {currentStep === 5 && (
                  <WrittenReviewStep
                    bossName={bossName}
                    onComplete={handleFinalSubmit}
                    initialData={{
                      headline: formData?.review_headline || "",
                      content: formData?.review_content || "",
                    }}
                    key={`written-review-${draftId}`}
                  />
                )}
              </>
            )}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Save & Exit Dialog */}
      <Dialog open={showExitDialog} onOpenChange={setShowExitDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Save and exit review?</DialogTitle>
            <DialogDescription className="space-y-3 pt-3">
              <p>
                Your progress has been saved. You can continue your review later
                by:
              </p>
              <div className="space-y-2 pl-5">
                <p className="flex items-start gap-2">
                  <span className="font-medium text-primary">•</span>
                  Clicking "Write a Review" on {bossName}'s profile
                </p>
                <p className="flex items-start gap-2">
                  <span className="font-medium text-primary">•</span>
                  Finding this review in your "Incomplete Reviews" section on
                  your profile
                </p>
              </div>
              <p className="text-xs text-muted-foreground pt-2">
                Note: Saved reviews are kept for 7 days before being
                automatically deleted
              </p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex sm:justify-between">
            <Button variant="ghost" onClick={() => setShowExitDialog(false)}>
              Continue Writing
            </Button>
            <Button variant="default" onClick={handleConfirmExit}>
              Save & Exit
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Dialog - Updated messaging */}
      <Dialog
        open={showSuccessDialog}
        onOpenChange={() => {
          setShowSuccessDialog(false);
          onClose();
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {isNewReviewOnModeration
                ? "Review Submitted for Moderation"
                : "Review Published Successfully!"}
            </DialogTitle>
            <DialogDescription className="space-y-3 pt-3">
              {isNewReviewOnModeration ? (
                <>
                  <p>
                    Thank you for sharing your experience with {bossName}. Your
                    review has been submitted for moderation.
                  </p>
                  <div className="space-y-2 pl-5">
                    <p className="flex items-start gap-2 text-sm">
                      <span className="font-medium text-primary">•</span>
                      Please wait while we review your submission
                    </p>
                    <p className="flex items-start gap-2 text-sm">
                      <span className="font-medium text-primary">•</span>
                      This helps us maintain high quality content for our
                      community
                    </p>
                  </div>
                </>
              ) : (
                <>
                  <p>
                    Thank you for sharing your experience with {bossName}. Your
                    review is now live!
                  </p>
                  <div className="space-y-2 pl-5">
                    <p className="flex items-start gap-2 text-sm">
                      <span className="font-medium text-primary">•</span>
                      Your review will help others make informed career
                      decisions
                    </p>
                    <p className="flex items-start gap-2 text-sm">
                      <span className="font-medium text-primary">•</span>
                      We use AI moderation to ensure all content meets our
                      community guidelines
                    </p>
                  </div>
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            {!isNewReviewOnModeration && (
              <Button onClick={handleGoToCreatedReview}>View My Review</Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Already Reviewed Dialog - Modified to only close via button */}
      <Dialog
        open={showAlreadyReviewedDialog}
        onOpenChange={() => {
          setShowAlreadyReviewedDialog(false);
          onClose();
        }}
      >
        <DialogContent
          className="sm:max-w-md"
          onPointerDownOutside={(e) => {
            e.preventDefault();
          }}
          onEscapeKeyDown={(e) => {
            e.preventDefault();
          }}
        >
          <DialogHeader>
            <DialogTitle>Review Already Submitted</DialogTitle>
            <DialogDescription className="space-y-3 pt-3">
              <p>You have already submitted a review for {bossName}.</p>
              <p>
                Each user can only submit one review per boss to ensure fair and
                accurate ratings.
              </p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={handleGoToMyReview}>View My Review</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}