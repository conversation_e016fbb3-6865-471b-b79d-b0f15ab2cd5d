"use client";

import React, { useState, useEffect, useRef } from "react";
import { createClient } from "@/utils/supabase/client";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { Search, Building2, User2, Loader2, X } from "lucide-react";
import AddBossOrCompanyForm from "@/components/AddBossOrCompanyForm";
import { cn } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import logger from "@/utils/logger";
import SignInRequiredDialog from "@/components/dialogs/sign-in-required-dialog";
import { routes } from "@/utils/routes";

interface Suggestion {
  id: number;
  name: string;
  type: "boss" | "company";
}

interface AutocompleteProps {
  onSubmit: (query: string) => void;
  placeholder?: string;
}

const Autocomplete: React.FC<AutocompleteProps> = ({
  onSubmit,
  placeholder = "Search...", // default placeholder
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (query.length < 2) {
        setSuggestions([]);
        return;
      }

      setIsLoading(true);
      const supabase = createClient();

      try {
        const [{ data: bosses }, { data: companies }] = await Promise.all([
          supabase
            .from("bosses")
            .select("boss_id, name")
            .ilike("name", `%${query}%`)
            .is("deleted_at", null)
            .limit(5),
          supabase
            .from("companies")
            .select("company_id, name")
            .ilike("name", `%${query}%`)
            .limit(5),
        ]);

        const bossResults =
          bosses?.map((boss) => ({
            id: boss.boss_id,
            name: boss.name,
            type: "boss" as const,
          })) || [];
        const companyResults =
          companies?.map((company) => ({
            id: company.company_id,
            name: company.name,
            type: "company" as const,
          })) || [];

        setSuggestions([...bossResults, ...companyResults]);
      } catch (error) {
        logger.error("Error fetching suggestions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(fetchSuggestions, 300);
    return () => clearTimeout(debounceTimer);
  }, [query]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      router.push(`/find-boss?q=${encodeURIComponent(query.trim())}`);
    }
    setShowSuggestions(false);
  };

  const handleSuggestionClick = (suggestion: Suggestion) => {
    if (suggestion.type === "boss") {
      router.push(`/boss/${suggestion.id}`);
    } else {
      router.push(`/find-boss?q=${encodeURIComponent(suggestion.name)}`);
    }
    setShowSuggestions(false);
    setQuery('')
  };

  const clearSearch = () => {
    setQuery("");
    setShowSuggestions(false);
  };

  // Handle login action when user clicks Sign In button in dialog
  const handleLogin = () => {
    router.push(`/sign-in?redirect=${encodeURIComponent(pathname)}`);
  };

  return (
    <div className="relative w-full">
      <form onSubmit={handleSubmit}>
        <div className="relative flex items-center">
          <div className="absolute left-4 flex items-center pointer-events-none">
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            ) : (
              <Search className="h-4 w-4 text-muted-foreground" />
            )}
          </div>
          <Input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              setShowSuggestions(true);
            }}
            onFocus={() => setShowSuggestions(true)}
            placeholder={placeholder}
            className={cn(
              "w-full pl-10 pr-12 h-[40px] text-base",
              "bg-white/10 dark:bg-white/5",
              "border-white/20 dark:border-white/10",
              "text-foreground placeholder:text-muted-foreground",
              "hover:bg-white/20 dark:hover:bg-white/10",
              "focus:bg-white/20 dark:focus:bg-white/10",
              "rounded-2xl shadow-sm",
              "focus-visible:ring-1 focus-visible:ring-white/30",
              "focus:outline-none",
              showSuggestions &&
                query.length > 0 &&
                "rounded-b-none border-b-0 shadow-lg",
              "transition-all duration-200",
              "border border-input dark:border-input",
            )}
          />
          {query && (
            <button
              type="button"
              onClick={clearSearch}
              className="absolute right-4 text-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </form>

      {showSuggestions && query.length > 0 && (
        <div
          className={cn(
            "absolute w-full bg-white dark:bg-background",
            "border border-input dark:border-input",
            "rounded-b-2xl shadow-lg overflow-hidden",
            "border-t-0 mt-0 pt-2",
          )}
        >
          {suggestions.length > 0 ? (
            <div className="py-2">
              {/* Boss Suggestions */}
              <div className="px-4 py-1 text-xs font-medium text-muted-foreground">
                Bosses
              </div>
              {suggestions
                .filter((s) => s.type === "boss")
                .map((suggestion) => (
                  <button
                    key={`${suggestion.type}-${suggestion.id}`}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full px-4 py-2 text-sm text-left hover:bg-accent hover:text-accent-foreground flex items-center gap-3 transition-colors"
                  >
                    <User2 className="h-4 w-4 text-muted-foreground shrink-0" />
                    <span className="truncate text-foreground">
                      {suggestion.name}
                    </span>
                  </button>
                ))}

              {/* Company Suggestions */}
              <div className="mt-2 px-4 py-1 text-xs font-medium text-muted-foreground border-t">
                Companies
              </div>
              {suggestions
                .filter((s) => s.type === "company")
                .map((suggestion) => (
                  <button
                    key={`${suggestion.type}-${suggestion.id}`}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full px-4 py-2 text-sm text-left hover:bg-accent hover:text-accent-foreground flex items-center gap-3 transition-colors"
                  >
                    <Building2 className="h-4 w-4 text-muted-foreground shrink-0" />
                    <span className="truncate text-foreground">
                      {suggestion.name}
                    </span>
                  </button>
                ))}
            </div>
          ) : (
            <div className="py-6 text-center">
              {isLoading ? (
                <div className="text-muted-foreground text-sm">
                  Searching...
                </div>
              ) : (
                <div className="space-y-2 px-4">
                  <p className="text-muted-foreground text-sm">
                    No results found
                  </p>
                  <button
                    onClick={() => {
                      setQuery('')
                    }}
                    className="text-primary hover:underline text-sm"
                  >
                    <Link href={routes.addBoss}>Add a new boss</Link>
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add a New Boss</DialogTitle>
          </DialogHeader>
          <AddBossOrCompanyForm
            onClose={() => setShowAddForm(false)}
            onSubmit={(formData) => {
              console.log("Form submitted:", formData);
              setShowAddForm(false);
            }}
            useExternalDialog={true}
          />
        </DialogContent>
      </Dialog>

      {/* Sign-in Required Dialog */}
      <SignInRequiredDialog
        showDialog={showAuthDialog}
        setShowDialog={setShowAuthDialog}
        actionClick={handleLogin}
        title="Sign in required"
        description="Please sign in to add a new boss."
      />
    </div>
  );
};

export default Autocomplete;
