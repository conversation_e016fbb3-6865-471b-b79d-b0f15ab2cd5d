"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@/components/ui/dialog";
import type { WorkExperience } from "@/types/boss";
import BossReviewFormV2 from "@/components/forms/BossReviewFormV2";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Drawer } from "@/components/ui/drawer";

interface ReviewFormModalProps {
  open: boolean;
  onClose: () => void;
  bossId?: string;
  bossName?: string;
  workExperiences?: WorkExperience[];
  onSubmit: () => void;
}

export default function ReviewFormModal({
  open,
  onClose,
  bossId = "",
  bossName = "",
  workExperiences = [],
  onSubmit,
}: ReviewFormModalProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="max-w-2xl max-h-[85vh] overflow-y-auto px-2">
          <DialogTitle className="sr-only">Review {bossName}</DialogTitle>
          <BossReviewFormV2
            bossId={bossId}
            bossName={bossName}
            workExperiences={workExperiences}
            onClose={onClose}
            onSubmit={onSubmit}
            isDesktop={true}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onClose={() => handleOpenChange(false)}>
      <BossReviewFormV2
        bossId={bossId}
        bossName={bossName}
        workExperiences={workExperiences}
        onClose={onClose}
        onSubmit={onSubmit}
      />
    </Drawer>
  );
}
