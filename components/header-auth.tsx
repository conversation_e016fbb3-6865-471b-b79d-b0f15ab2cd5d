'use client';

import { useAuth } from '@/hooks/useAuth';
import Link from 'next/link';
import { createClient } from '@/utils/supabase/client';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { UserRoundCheck, UserRoundCog, LogIn, LogOut } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {useUser} from "@/contexts/UserContext";

interface AuthButtonProps {
  menuItem?: boolean;
}

export default function AuthButton({ menuItem = false }: AuthButtonProps) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const signOut = async () => {
    const supabase = createClient();
    await supabase.auth.signOut();
    router.refresh();
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  // If it's a menu item, return a simple div with the appropriate content
  if (menuItem) {
    if (user) {
      return (
        <div 
          onClick={signOut}
          className="flex items-center gap-2 w-full px-4 py-2"
        >
          <LogOut className="h-4 w-4 shrink-0" />
          <span className="text-sm">Sign Out</span>
        </div>
      );
    }
    return (
      <Link 
        href={`/sign-in?redirect=${encodeURIComponent(pathname)}`}
        className="flex items-center gap-2 w-full px-4 py-2"
      >
        <LogIn className="h-4 w-4 shrink-0" />
        <span className="text-sm">Sign In</span>
      </Link>
    );
  }

  // Regular button version for other uses
  if (user) {
    return (
      <DropdownMenu>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <UserRoundCheck className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>Profile & Settings</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>{user.email}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link href="/profile">Profile</Link>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={signOut}>
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="ghost" size="icon" asChild>
            <Link href={`/sign-in?redirect=${encodeURIComponent(pathname)}`}>
              <UserRoundCog className="h-5 w-5" />
            </Link>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Sign In</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
