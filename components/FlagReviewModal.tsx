import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { createClient } from "@/utils/supabase/client";
import { validateInput, ValidationOptions } from "@/utils/input-validator";
import { AlertCircle } from "lucide-react";

interface FlagReport {
  primaryReason: string;
  specificDetails: string;
  additionalContext?: string;
  customSpecificDetails: string;
  urgencyLevel: "standard" | "urgent";
}

// Validation options for text inputs
const TEXT_VALIDATION_OPTIONS: ValidationOptions = {
  minLength: 1,
  maxLength: 500,
  allowHtml: false,
  allowUrls: false,
  allowSpecialChars: true,
  required: false,
};

interface FlagReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  reviewId: string;
  onFlagSubmit: (reviewId: string) => void;
}

const FlagReviewModal: React.FC<FlagReviewModalProps> = ({
  isOpen,
  onClose,
  reviewId,
  onFlagSubmit,
}) => {
  const [flagReport, setFlagReport] = useState<FlagReport>({
    primaryReason: "",
    specificDetails: "",
    additionalContext: "",
    urgencyLevel: "standard",
    customSpecificDetails: "",
  });

  // Track errors for each input field separately
  const [errors, setErrors] = useState<{
    primaryReason: string | null;
    specificDetails: string | null;
    customSpecificDetails: string | null;
    additionalContext: string | null;
    general: string | null;
  }>({
    primaryReason: null,
    specificDetails: null,
    customSpecificDetails: null,
    additionalContext: null,
    general: null,
  });

  const supabase = createClient();

  useEffect(() => {
    if (flagReport.primaryReason === "Other Concern") {
      setFlagReport((prev) => ({ ...prev, specificDetails: "" }));
    }
    // Clear validation errors when inputs change
    setErrors({
      primaryReason: null,
      specificDetails: null,
      customSpecificDetails: null,
      additionalContext: null,
      general: null,
    });
  }, [
    flagReport.primaryReason,
    flagReport.specificDetails,
    flagReport.additionalContext,
    flagReport.customSpecificDetails,
  ]);

  // Validate text input with field identification
  const validateTextInput = (
    text: string,
    fieldName: keyof typeof errors,
  ): boolean => {
    if (!text) return true; // Allow empty strings for optional fields

    const result = validateInput(text, TEXT_VALIDATION_OPTIONS);
    if (!result.valid) {
      setErrors((prev) => ({
        ...prev,
        [fieldName]: result.message || "Invalid input",
      }));
      return false;
    }
    return true;
  };

  const handleFlagSubmit = async () => {
    // Reset all errors first
    setErrors({
      primaryReason: null,
      specificDetails: null,
      customSpecificDetails: null,
      additionalContext: null,
      general: null,
    });

    let hasErrors = false;

    // Validate required fields
    if (!flagReport.primaryReason) {
      setErrors((prev) => ({
        ...prev,
        primaryReason: "Please select a primary reason",
      }));
      hasErrors = true;
    }

    if (
      flagReport.primaryReason !== "Other Concern" &&
      !flagReport.specificDetails
    ) {
      setErrors((prev) => ({
        ...prev,
        specificDetails: "Please select specific details",
      }));
      hasErrors = true;
    }

    // Validate text inputs
    if (
      flagReport.primaryReason === "Other Concern" &&
      !validateTextInput(
        flagReport.customSpecificDetails,
        "customSpecificDetails",
      )
    ) {
      hasErrors = true;
    }

    if (
      flagReport.additionalContext &&
      !validateTextInput(flagReport.additionalContext, "additionalContext")
    ) {
      hasErrors = true;
    }

    if (hasErrors) {
      return;
    }

    // All validations passed, proceed with submission
    try {
      await supabase
        .from("reviews")
        .update({
          status: "flagged",
          // flag_reason: flagReport.primaryReason,
          // flag_details: flagReport.specificDetails,
          // flag_context: flagReport.additionalContext,
          // flag_urgency: flagReport.urgencyLevel
        })
        .eq("review_id", reviewId);
      onFlagSubmit(reviewId);
      onClose();
    } catch (error) {
      console.error("Error submitting flag report:", error);
      setErrors((prev) => ({
        ...prev,
        general: "Failed to submit report. Please try again.",
      }));
    }
  };

  const renderSpecificDetailsOptions = () => {
    switch (flagReport.primaryReason) {
      case "Inappropriate or Offensive Content":
        return (
          <>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Contains harassment or hate speech"
                id="harassment"
              />
              <Label htmlFor="harassment">
                Contains harassment or hate speech
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Shares private/personal information"
                id="private-info"
              />
              <Label htmlFor="private-info">
                Shares private/personal information
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Discusses non-work matters"
                id="non-work"
              />
              <Label htmlFor="non-work">Discusses non-work matters</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Contains profanity or inappropriate language"
                id="profanity"
              />
              <Label htmlFor="profanity">
                Contains profanity or inappropriate language
              </Label>
            </div>
          </>
        );
      case "Inaccurate or Misleading Information":
        return (
          <>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Review is not based on direct experience"
                id="not-direct"
              />
              <Label htmlFor="not-direct">
                Review is not based on direct experience
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="Contains false claims" id="false-claims" />
              <Label htmlFor="false-claims">Contains false claims</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Includes unverified accusations"
                id="unverified"
              />
              <Label htmlFor="unverified">
                Includes unverified accusations
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="References events that didn't happen"
                id="didnt-happen"
              />
              <Label htmlFor="didnt-happen">
                References events that didn't happen
              </Label>
            </div>
          </>
        );
      case "Not a Genuine Review":
        return (
          <>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Appears to be self-review"
                id="self-review"
              />
              <Label htmlFor="self-review">Appears to be self-review</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Multiple reviews from same person"
                id="multiple"
              />
              <Label htmlFor="multiple">
                Multiple reviews from same person
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Review from non-employee"
                id="non-employee"
              />
              <Label htmlFor="non-employee">Review from non-employee</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="Spam content" id="spam" />
              <Label htmlFor="spam">Spam content</Label>
            </div>
          </>
        );
      case "Technical Problem":
        return (
          <>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="Duplicate review" id="duplicate" />
              <Label htmlFor="duplicate">Duplicate review</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="Wrong company/person" id="wrong-company" />
              <Label htmlFor="wrong-company">Wrong company/person</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="Display or formatting issue"
                id="formatting"
              />
              <Label htmlFor="formatting">Display or formatting issue</Label>
            </div>
          </>
        );
      case "Other Concern":
        return (
          <div>
            <Textarea
              placeholder="Please explain your concern"
              value={flagReport.customSpecificDetails}
              onChange={(e) => {
                const newValue = e.target.value;
                setFlagReport({
                  ...flagReport,
                  customSpecificDetails: newValue,
                });
                validateTextInput(newValue, "customSpecificDetails");
              }}
              className={errors.customSpecificDetails ? "border-red-500" : ""}
            />
            {errors.customSpecificDetails && (
              <p className="text-sm text-red-500 mt-1">
                {errors.customSpecificDetails}
              </p>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Report Review</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label className={errors.primaryReason ? "text-red-500" : ""}>
              Primary Report Reason {errors.primaryReason && "- Required"}
            </Label>
            <RadioGroup
              onValueChange={(value) =>
                setFlagReport({ ...flagReport, primaryReason: value })
              }
              value={flagReport.primaryReason}
              className={
                errors.primaryReason
                  ? "border border-red-500 rounded-md p-2"
                  : ""
              }
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="Inappropriate or Offensive Content"
                  id="inappropriate"
                />
                <Label htmlFor="inappropriate">
                  Inappropriate or Offensive Content
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="Inaccurate or Misleading Information"
                  id="inaccurate"
                />
                <Label htmlFor="inaccurate">
                  Inaccurate or Misleading Information
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="Not a Genuine Review" id="not-genuine" />
                <Label htmlFor="not-genuine">Not a Genuine Review</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="Technical Problem" id="technical" />
                <Label htmlFor="technical">Technical Problem</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="Other Concern" id="other" />
                <Label htmlFor="other">Other Concern</Label>
              </div>
            </RadioGroup>
          </div>
          {flagReport.primaryReason && (
            <div>
              <Label className={errors.specificDetails ? "text-red-500" : ""}>
                Specific Details {errors.specificDetails && "- Required"}
              </Label>
              <RadioGroup
                onValueChange={(value) =>
                  setFlagReport({ ...flagReport, specificDetails: value })
                }
                value={flagReport.specificDetails}
                className={
                  errors.specificDetails
                    ? "border border-red-500 rounded-md p-2"
                    : ""
                }
              >
                {renderSpecificDetailsOptions()}
              </RadioGroup>
            </div>
          )}
          <div>
            <Label>Additional Context (Optional)</Label>
            <Textarea
              placeholder="Provide any additional details that would help our moderators review this report"
              value={flagReport.additionalContext}
              onChange={(e) => {
                const newValue = e.target.value;
                setFlagReport({ ...flagReport, additionalContext: newValue });
                // Only validate if there's content
                if (newValue) validateTextInput(newValue, "additionalContext");
              }}
              className={errors.additionalContext ? "border-red-500" : ""}
            />
            {errors.additionalContext && (
              <p className="text-sm text-red-500 mt-1">
                {errors.additionalContext}
              </p>
            )}
          </div>
          <div>
            <Label>Urgency Level</Label>
            <RadioGroup
              onValueChange={(value) =>
                setFlagReport({
                  ...flagReport,
                  urgencyLevel: value as "standard" | "urgent",
                })
              }
              value={flagReport.urgencyLevel}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="standard" id="standard" />
                <Label htmlFor="standard">Standard Review</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="urgent" id="urgent" />
                <Label htmlFor="urgent">Urgent Attention Needed</Label>
              </div>
            </RadioGroup>
          </div>
        </div>
        {(errors.general || errors.primaryReason || errors.specificDetails) && (
          <div className="flex items-start gap-2 p-3 mb-2 bg-red-50 text-red-800 rounded-md">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <p className="text-sm">
              {errors.general || errors.primaryReason || errors.specificDetails}
            </p>
          </div>
        )}
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleFlagSubmit}>Submit Report</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FlagReviewModal;
