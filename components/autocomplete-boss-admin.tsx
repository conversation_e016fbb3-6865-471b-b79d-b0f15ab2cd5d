"use client";

import React, { useState, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, User2, Loader2, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";
import useDebounce from "@/hooks/useDebounce";
import logger from "@/utils/logger";

interface BossSearchResult {
  id: string;
  name: string;
  type: "boss";
}

interface BossData {
  boss_id: number;
  name: string;
  reviews: { count: number }[];
  work_experiences: { count: number }[];
}

interface AutocompleteProps {
  onSubmit: (bossName: string, bossId: string) => void;
  placeholder?: string;
}

const BossAdminAutocomplete: React.FC<AutocompleteProps> = ({
  onSubmit,
  placeholder = "Search...", // default placeholder
}) => {
  const [query, setQuery] = useState("");
  const [selectedBossId, setSelectedBossId] = useState<string>("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);

  // Debounce the query to avoid too many API calls
  const debouncedQuery = useDebounce(query, 300);

  // Fetch boss search results using react-query
  const fetchBossSearchResults = async (
    searchQuery: string,
  ): Promise<BossSearchResult[]> => {
    if (searchQuery.length < 2) {
      return [];
    }

    const supabase = createClient();

    try {
      const { data: bossesData, error } = await supabase
        .from("bosses")
        .select(
          `
          *,
          reviews (count),
          work_experiences (count)
        `,
        )
        .order("name")
        .ilike("name", `%${searchQuery}%`);

      if (error) {
        throw error;
      }

      const bossResults: BossSearchResult[] =
        bossesData?.map((boss: BossData) => ({
          id: boss.boss_id,
          name: boss.name,
          type: "boss" as const,
        })) || [];

      return bossResults;
    } catch (error) {
      logger.error("Error fetching boss search suggestions:", error);
      throw error;
    }
  };


  const { data: suggestions = [], isLoading } = useQuery({
    queryKey: ["admin-boss-search", debouncedQuery],
    queryFn: () => fetchBossSearchResults(debouncedQuery),
    enabled: showSuggestions && debouncedQuery.length >= 2,
    staleTime: 1000 * 60 * 5,
    retry: 1,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(query, selectedBossId);
    setShowSuggestions(false);
  };

  const handleSuggestionClick = (suggestion: BossSearchResult) => {
    setQuery(suggestion.name);
    setSelectedBossId(suggestion.id);
    setShowSuggestions(false);
    onSubmit(suggestion.name, suggestion.id);
  };

  const clearSearch = () => {
    setQuery("");
    setSelectedBossId("");
    setShowSuggestions(false);
    onSubmit("", "");
  };

  return (
    <div className="relative w-full max-w-full">
      <form
        onSubmit={handleSubmit}
        className="flex flex-row justify-center items-center gap-4 w-full"
      >
        <div
          ref={inputContainerRef}
          className="relative flex items-center flex-1 min-w-0"
        >
          <div className="absolute left-4 flex items-center pointer-events-none">
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            ) : (
              <Search className="h-4 w-4 text-muted-foreground" />
            )}
          </div>
          <Input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              setSelectedBossId("");
              setShowSuggestions(true);
            }}
            onFocus={() => setShowSuggestions(true)}
            placeholder={placeholder}
            className={cn(
              "w-full max-w-full pl-10 pr-12 h-[40px] text-base",
              "bg-white/10 dark:bg-white/5",
              "border-white/20 dark:border-white/10",
              "text-foreground placeholder:text-muted-foreground",
              "hover:bg-white/20 dark:hover:bg-white/10",
              "focus:bg-white/20 dark:focus:bg-white/10",
              "rounded-2xl shadow-sm",
              "focus-visible:ring-1 focus-visible:ring-white/30",
              "focus:outline-none",
              showSuggestions &&
                query.length > 0 &&
                "rounded-b-none border-b-0 shadow-lg",
              "transition-all duration-200",
              "border border-input dark:border-input",
            )}
          />
          <button
            type="button"
            onClick={clearSearch}
            className="absolute right-4 text-foreground hover:text-foreground"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </form>

      {showSuggestions && query.length > 0 && (
        <div
          className={cn(
            "absolute bg-white dark:bg-background",
            "border border-input dark:border-input",
            "rounded-b-2xl shadow-lg overflow-hidden",
            "border-t-0 mt-0 pt-2",
            "z-50",
          )}
          style={{
            width: inputContainerRef.current?.offsetWidth || "100%",
            left: inputContainerRef.current?.offsetLeft || 0,
            top:
              (inputContainerRef.current?.offsetTop || 0) +
              (inputContainerRef.current?.offsetHeight || 40),
          }}
        >
          {suggestions.length > 0 ? (
            <div className="py-2">
              {suggestions
                .filter((s: BossSearchResult) => s.type === "boss")
                .map((suggestion: BossSearchResult) => (
                  <button
                    key={`${suggestion.type}-${suggestion.id}`}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full px-4 py-2 text-sm text-left hover:bg-accent hover:text-accent-foreground flex items-center gap-3 transition-colors"
                  >
                    <User2 className="h-4 w-4 text-muted-foreground shrink-0" />
                    <span className="truncate text-foreground">
                      {suggestion.name}
                    </span>
                  </button>
                ))}
            </div>
          ) : (
            <div className="py-6 text-center">
              {isLoading && (
                <div className="text-muted-foreground text-sm">
                  Searching...
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BossAdminAutocomplete;
