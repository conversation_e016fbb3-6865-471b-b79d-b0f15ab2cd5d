"use client";

import React, { useState, useEffect, useRef } from "react";
import { createClient } from "@/utils/supabase/client";
import { Input } from "@/components/ui/input";
import { Building2, Loader2, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface CompanyAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  onClick?: () => void;
  placeholder?: string;
  className?: string;
  maxLength?: number;
}

const CompanyAutocomplete: React.FC<CompanyAutocompleteProps> = ({
  value,
  onChange,
  onClick,
  placeholder = "Enter company name",
  className,
  maxLength = 50,
}) => {
  const [suggestions, setSuggestions] = useState<
    { company_id: string; name: string }[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (value.length < 2) {
        setSuggestions([]);
        return;
      }

      setIsLoading(true);
      const supabase = createClient();

      try {
        const { data: companies } = await supabase
          .from("companies")
          .select("company_id, name")
          .ilike("name", `%${value}%`)
          .limit(5);

        setSuggestions(companies || []);
      } catch (error) {
        console.error("Error fetching company suggestions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(fetchSuggestions, 300);
    return () => clearTimeout(debounceTimer);
  }, [value]);

  // Handle clicks outside the component
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        showSuggestions
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showSuggestions]);

  const handleSuggestionClick = (companyName: string) => {
    onChange(companyName);
    setShowSuggestions(false);
    // Don't call onClick here as it overrides the selectedCompany value
    // that was just set by onChange
  };

  const clearSearch = () => {
    onChange("");
    setShowSuggestions(false);
  };

  return (
    <div ref={containerRef} className="relative w-full">
      <div className="relative flex items-center">
        <div className="absolute left-4 flex items-center pointer-events-none">
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          ) : (
            <Building2 className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
        <Input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => {
            onChange(e.target.value);
            setShowSuggestions(e.target.value.length >= 2);
          }}
          onFocus={() => setShowSuggestions(value.length >= 2)}
          onClick={onClick}
          placeholder={placeholder}
          maxLength={maxLength}
          className={cn(
            "w-full !pl-10 !pr-8 text-base font-medium",
            "rounded-lg border-2 focus-visible:ring-0 focus-visible:ring-offset-0",
            "rounded-lg p-3 h-auto",
            // showSuggestions && value.length > 0 && "rounded-b-none border-b-0",
            className,
          )}
        />
        {value && (
          <button
            type="button"
            onClick={clearSearch}
            className="absolute right-3 text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {showSuggestions && value.length > 0 && (
        <div
          className={cn(
            "absolute w-full bg-background",
            "border border-input",
            "rounded-lg shadow-lg overflow-hidden",
            "border-t-0 mt-2 z-10",
          )}
        >
          {suggestions.length > 0 ? (
            <div className="py-1">
              {suggestions.map((company) => (
                <button
                  key={company.company_id}
                  onClick={() => handleSuggestionClick(company.name)}
                  className="w-full px-4 py-2 text-sm text-left hover:bg-accent hover:text-accent-foreground flex items-center gap-3 transition-colors"
                >
                  <Building2 className="h-4 w-4 text-muted-foreground shrink-0" />
                  <span className="truncate text-foreground">
                    {company.name}
                  </span>
                </button>
              ))}
            </div>
          ) : (
            <div className="py-3 text-center">
              {isLoading ? (
                <div className="text-muted-foreground text-sm">
                  Searching...
                </div>
              ) : (
                <div className="text-muted-foreground text-sm">
                  No companies found. Continue typing to add a new company.
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CompanyAutocomplete;
