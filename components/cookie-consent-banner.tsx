// components/CookieConsentBanner.tsx
"use client";

import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON>, <PERSON>ie } from "lucide-react";
import { cn } from "@/lib/utils";

const CookieConsentBanner = () => {
  const [visible, setVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const consent = Cookies.get("cookie_consent");
    if (!consent) {
      setVisible(true);
      // Add slight delay for smooth entrance animation
      setTimeout(() => setIsAnimating(true), 100);
    }
  }, []);

  const acceptCookies = () => {
    Cookies.set("cookie_consent", "true", { expires: 365 });
    setIsAnimating(false);
    // Delay hiding to allow exit animation
    setTimeout(() => setVisible(false), 300);
  };

  const dismissBanner = () => {
    setIsAnimating(false);
    setTimeout(() => setVisible(false), 300);
  };

  if (!visible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 sm:p-6">
      <div className="container-md">
        <Card
          className={cn(
            "backdrop-blur-sm backdrop-saturate-150 bg-card/95 border shadow-lg transition-all duration-300 ease-out",
            isAnimating
              ? "translate-y-0 opacity-100"
              : "translate-y-full opacity-0"
          )}
        >
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              {/* Icon */}
              <div className="flex-shrink-0">
                <div className="w-10 h-10 rounded-lg bg-[#7322A5]/10 flex items-center justify-center">
                  <Cookie className="w-5 h-5 text-[#7322A5]" />
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 space-y-2">
                <h3 className="text-sm font-medium text-foreground">
                  We use cookies
                </h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  We use cookies to enhance your user experience. By continuing to use this site,
                  you agree to our use of cookies.
                </p>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2 w-full sm:w-auto">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={dismissBanner}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <X className="w-4 h-4" />
                </Button>
                <Button
                  onClick={acceptCookies}
                  size="sm"
                  className="bg-[#7322A5] hover:bg-[#7322A5]/90 text-white hover:-translate-y-0.5 transition-all duration-200 flex-1 sm:flex-none"
                >
                  Accept
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CookieConsentBanner;
