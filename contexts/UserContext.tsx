'use client';

import { createClient } from '@/utils/supabase/client';
import { User } from '@supabase/supabase-js';
import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import {usePathname} from "next/navigation";

type UserContextType = {
  user: User | null;
  savedBosses: SavedBoss[];
  isLoading: boolean;
  refreshUser: () => Promise<void>;
  userRoleId: number | null;
  signOut: () => void
  votes: Votes
};

type SavedBoss = {
  id: string;
  user_id: string;
  boss_id: string;
  created_at: string;
}

type Votes= {
  review_id: number
  is_positive: boolean
}[]

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [userRole, setUserRole] = useState<number | null>(null)
  const [savedBosses, setSavedBosses] = useState<SavedBoss[]>([]);
  const [votes, setVotes] = useState<Votes>([])

  const [isLoading, setIsLoading] = useState(true);

  const pathname = usePathname();

  const fetchUser = async () => {
    try {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);

      if (user) {
        const [savedBossesRes, userAdditionalRes, reviewVotesRes] = await Promise.all([
          supabase.from('saved_bosses').select('*').eq('user_id', user.id),
          supabase.from('users').select('*').eq('id', user.id).single(),
          supabase.from('review_votes').select('review_id, is_positive').eq('user_id', user.id),
        ]);

        const { data: savedBosses, error: savedBossesError } = savedBossesRes;
        const { data: userAdditionalData, error: userAdditionalError } = userAdditionalRes;
        const { data: userReviewVotes, error: reviewVotesError } = reviewVotesRes;

        setSavedBosses(savedBosses ?? []);
        setVotes(userReviewVotes ?? []);
        setUserRole(userAdditionalData?.role_id || null);
      }
    } catch (error)  {
      console.error('Error fetching user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Set up auth state change listener
    const supabase = createClient();
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setUser(session?.user ?? null);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = () => {
    setUser(null)
    setVotes([])
    setSavedBosses([])
    setUserRole(null)
  }

  useEffect(() => {
    fetchUser();
  }, [pathname]);

  return (
    <UserContext.Provider value={{ user, isLoading, savedBosses, refreshUser: fetchUser, userRoleId: userRole, votes, signOut }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}