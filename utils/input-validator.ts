/**
 * Common banned words and patterns for input validation
 */
export const  BANNED_WORDS = [
  "damn", "hell", "shit", "fuck", "idiot", "buy now", "click here",
  "free money", "visit this site", "cunt", "piss", "asshole", "dick", "cock",
  "pussy", "nigger", "slut", "kill", "f*ck", "sh!t", "cu*t", "d!ck", "@sshole",
  "b!tch", "p*ssy", "b@stard", "drop", "database", "delete", "update", "insert",
  "ALTER", "TRUNCATE", "GRANT", "REVOKE", "CREATE", "INDEX", "TRIGGER",
  "FUNCTION", "VIEW", "SCHEM<PERSON>", "ROL<PERSON>", "USER", "PASSWORD", "EXPLAIN", "ANALYZE",
];

/**
 * Input validation options
 */
export interface ValidationOptions {
  minLength?: number;
  maxLength?: number;
  allowHtml?: boolean;
  allowUrls?: boolean;
  allowSpecialChars?: boolean;
  customBannedWords?: string[];
  required?: boolean;
}

/**
 * Validation result
 */
export interface ValidationResult {
  valid: boolean;
  message?: string;
}

/**
 * Checks if text contains profanity or banned words
 * @param text - The text to check
 * @param customBannedWords - Optional additional banned words
 * @returns boolean indicating if profanity was found
 */
const containsProfanity = (text: string, customBannedWords?: string[]): boolean => {
  const lowerText = text.toLowerCase();
  const allBannedWords = customBannedWords
    ? [...BANNED_WORDS, ...customBannedWords]
    : BANNED_WORDS;

  return allBannedWords.some((word) => lowerText.includes(word));
};

/**
 * Validates input text based on provided options
 * @param value - The text to validate
 * @param options - Validation options
 * @returns ValidationResult object with valid flag and optional error message
 */
export const validateInput = (
  value: string,
  options: ValidationOptions = {}
): ValidationResult => {
  const {
    minLength = 1,
    maxLength = 255,
    allowHtml = false,
    allowUrls = false,
    allowSpecialChars = true,
    customBannedWords,
    required = true,
  } = options;

  // Handle empty input
  if (!value || value.trim() === '') {
    return required
      ? { valid: false, message: 'This field is required' }
      : { valid: true };
  }

  // Check length constraints
  if (value.trim().length < minLength) {
    return {
      valid: false,
      message: `Minimum length is ${minLength} characters`
    };
  }

  if (value.trim().length > maxLength) {
    return {
      valid: false,
      message: `Maximum length is ${maxLength} characters`
    };
  }

  // Check for profanity
  if (containsProfanity(value, customBannedWords)) {
    return {
      valid: false,
      message: 'Input contains inappropriate language'
    };
  }

  // Check for URLs
  if (!allowUrls && /https?:\/\/|www\./i.test(value)) {
    return {
      valid: false,
      message: 'URLs are not allowed in this field'
    };
  }

  // Check for HTML tags
  if (!allowHtml && /<[^>]*>/g.test(value)) {
    return {
      valid: false,
      message: 'HTML tags are not allowed in this field'
    };
  }

  // Check for special characters
  if (!allowSpecialChars && /[<>{}[\]\\\/]/.test(value)) {
    return {
      valid: false,
      message: 'Special characters are not allowed in this field'
    };
  }

  // If all checks pass
  return { valid: true };
};

/**
 * Validates email format
 * @param email - The email to validate
 * @returns ValidationResult object with valid flag and optional error message
 */
export const validateEmail = (email: string): ValidationResult => {
  if (!email || email.trim() === '') {
    return { valid: false, message: 'Email is required' };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { valid: false, message: 'Please enter a valid email address' };
  }

  return { valid: true };
};

/**
 * Validates URL format
 * @param url - The URL to validate
 * @param options - Additional validation options
 * @returns ValidationResult object with valid flag and optional error message
 */
export const validateUrl = (
  url: string,
  options: { required?: boolean; type?: 'linkedin' | 'general' } = {}
): ValidationResult => {
  const { required = true, type = 'general' } = options;

  if (!url || url.trim() === '') {
    return required
      ? { valid: false, message: 'URL is required' }
      : { valid: true };
  }

  // Basic URL validation
  const urlRegex = /^https?:\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,}(:[0-9]{1,5})?(\/.*)?$/i;
  if (!urlRegex.test(url)) {
    return { valid: false, message: 'Please enter a valid URL' };
  }

  // LinkedIn specific validation
  if (type === 'linkedin') {
    // Allow either a full LinkedIn URL or just a username
    const linkedinUrlRegex = /^https?:\/\/([a-z]{2,3}\.)?linkedin\.com\/.*/i;
    const usernameRegex = /^[a-zA-Z0-9_-]+$/;

    if (!linkedinUrlRegex.test(url) && !usernameRegex.test(url)) {
      return {
        valid: false,
        message: 'Please enter a valid LinkedIn URL (e.g., https://www.linkedin.com/in/username) or just the username'
      };
    }
  }

  return { valid: true };
};