import { createClient } from "@/utils/supabase/client";
//
// const DEFAULT_REDIRECT_URL = "https://www.boss.reviews/auth/callback";
//
// export const googleSignIn = async (redirectPath?: string) => {
//   const supabase = createClient();
//
//   // Build the redirect URL with the next parameter if provided
//   const redirectTo = redirectPath
//     ? `${DEFAULT_REDIRECT_URL}?next=${encodeURIComponent(redirectPath)}`
//     : DEFAULT_REDIRECT_URL;
//
//   await supabase.auth.signInWithOAuth({
//     provider: "google",
//     options: {
//       redirectTo,
//     },
//   });
// };
//
// export const linkedInSignIn = async (redirectPath?: string) => {
//   const supabase = createClient();
//
//   // Build the redirect URL with the next parameter if provided
//   const redirectTo = redirectPath
//     ? `${DEFAULT_REDIRECT_URL}?next=/add-boss`
//     : DEFAULT_REDIRECT_URL;
//
//   await supabase.auth.signInWithOAuth({
//     provider: "linkedin_oidc",
//   });
// };

const buildRedirectTo = (redirectPath?: string | null) => {
  return redirectPath
    ? `${REDIRECT_TO_URL}?next=${encodeURIComponent(redirectPath)}`
    : REDIRECT_TO_URL;
};

const REDIRECT_TO_URL = "https://www.tryamplify.app/auth/callback";
export const googleSignIn = async (redirectPath?: string | null) => {
  const supabase = createClient();
  await supabase.auth.signInWithOAuth({
    provider: "google",
    options: {
      redirectTo: buildRedirectTo(redirectPath),
    },
  });
};

export const linkedInSignIn = async (redirectPath?: string | null) => {
  const supabase = createClient();
  await supabase.auth.signInWithOAuth({
    provider: "linkedin_oidc",
    options: {
      redirectTo: buildRedirectTo(redirectPath),
    },
  });
};
