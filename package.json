{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "update-types": "supabase gen types typescript --project-id ppgdjtqdcysehqfbzedw > types/database.types.ts"}, "dependencies": {"@microsoft/clarity": "^1.0.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.4", "@sentry/nextjs": "^9.12.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tanstack/react-query": "^5.74.3", "autoprefixer": "10.4.17", "axios": "^1.8.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.6.5", "geist": "^1.2.1", "js-cookie": "^3.0.5", "lucide-react": "^0.436.0", "next": "latest", "prettier": "^3.3.3", "react": "18.2.0", "react-dom": "18.2.0", "react-share": "^5.2.2", "sonner": "^2.0.3"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/js-cookie": "^3.0.6", "@types/node": "20.10.6", "@types/react": "18.2.46", "@types/react-dom": "18.2.18", "postcss": "8.4.33", "supabase": "latest", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "5.3.3"}}